import {BrowserAnimationsModule} from '@angular/platform-browser/animations'; // this is needed!
import {NgModule} from '@angular/core';
import {HttpClient, HttpClientModule} from '@angular/common/http';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {AppComponent} from './app.component';
import {CoreModule} from './core/core.module';
import {LayoutModule} from './layout/layout.module';
import {SharedModule} from './shared/shared.module';
import {RoutesModule} from './routes/routes.module';
import {MatButtonModule, MatCheckboxModule} from '@angular/material';
import {BsDatepickerModule} from 'ngx-bootstrap';
import {ReduxModule} from './redux/redux.module';
import {configureChartJS} from './configuration/chartJsConfiguration';
import {AuthServiceConfig, FacebookLoginProvider, GoogleLoginProvider, SocialLoginModule} from 'angular5-social-login';
// import { AuthHttpInterceptor } from './services/auth/auth-http.interceptor';
import { DatePipe } from '@angular/common';
import {CommonModule} from './common/common.module';
import {CondominioLoadGuard} from './services/condominioLoad.guard';

// https://github.com/ocombe/ng2-translate/issues/218
export function createTranslateLoader(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

configureChartJS();

export function getAuthServiceConfigs() {
  const config = new AuthServiceConfig(
    [
      {
        id: FacebookLoginProvider.PROVIDER_ID,
        provider: new FacebookLoginProvider('Your-Facebook-app-id')
      },
      {
        id: GoogleLoginProvider.PROVIDER_ID,
        provider: new GoogleLoginProvider('375200327254-46uf7dgfdg90ia35itdv1s388mle1fu9.apps.googleusercontent.com')
      },
    ]
  );
  return config;
}

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BsDatepickerModule.forRoot(),
    ReduxModule,
    HttpClientModule,
    BrowserAnimationsModule, // required for ng2-tag-input
    CoreModule,
    LayoutModule,
    SharedModule.forRoot(),
    RoutesModule,
    MatButtonModule,
    MatCheckboxModule,
    SocialLoginModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: (createTranslateLoader),
        deps: [HttpClient]
      }
    }),
    CommonModule
  ],
  providers: [ { provide: AuthServiceConfig,
    useFactory: getAuthServiceConfigs}, DatePipe, CondominioLoadGuard],
  bootstrap: [AppComponent]
})
export class AppModule {
}
