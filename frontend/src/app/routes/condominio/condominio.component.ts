import {Component, OnInit} from '@angular/core';
import {UserDataService} from '../../common/services/user-data/userData.service';
import {DialogModalActions} from '../../redux/dialogModal/actions';
import {successModalNoCondominiumsWithUnpaidBills} from './config/config';
import {Condomini} from '../../common/model/condomini/condomini.model';
import {OffersService} from '../../common/services/offers/offers.service';
import {Router} from '@angular/router';
import {ClientNotificationService} from '../../common/services/client-notification/client-notification.service';
import {ClientNotification} from '../../common/model/client-notification/client-notification.model';
import * as moment from 'moment';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {InvoiceService} from '../invoices/invoice.service';

@Component({
  selector: 'app-condominio',
  templateUrl: './condominio.component.html',
  styleUrls: ['./condominio.component.scss']
})
export class CondominioComponent implements OnInit {

/*  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;*/

  readonly FILTER_ALL_TEXT = 'Visualizza tutti i condomini';
  readonly FILTER_UNPAID_BILLS_TEXT = 'Filtra condomini per bollette non pagate';
  condominioList: Condomini[];
  condominioListIsFiltered: Condomini[];
  condominioListUnFiltered: Condomini[];
  clientIdSet = new Set();
  allNotificationsForClient = new Map<number, ClientNotification[]>();
  nullInfo: string;
  isFiltered = false;
  isShowSpinner = false;
  btnFilter = 'Filtra condomini per bollette non pagate';
  condominioFieldValue = 'Condominio';
  defaultYear = moment().year();
  downloadableYears: number[] = Array.from({length: this.defaultYear - 2022 + 1}, (_, i) => i + 2022);
  formGroupYearReport: FormGroup;
  showNotFindFileWindow: boolean;

  constructor(private userDataService: UserDataService, private clientNotificationService: ClientNotificationService,
              private dialogModalActions: DialogModalActions, private offersService: OffersService, private router: Router,
              private formBuilder: FormBuilder, private excelFileService: InvoiceService) {
  }

  ngOnInit() {
    this.isShowSpinner = true;
    this.userDataService.getUserCondominioInfo(localStorage.getItem('clientId')).subscribe(condominioList => {
      if (condominioList === null || condominioList.length === 0) {
        this.nullInfo = 'Condomini non ancora creato';
        this.isShowSpinner = false;
      } else {
        condominioList.forEach(infoElement => this.clientIdSet.add(infoElement['customerId']));
        this.clientNotificationService.getAllClientNotificationsForUserList(Array.from(this.clientIdSet)).subscribe(
          clientNotificationList => {
            if (clientNotificationList.length !== 0) {
              clientNotificationList.forEach(clientNotification => this.fillNotificationMap(clientNotification));
            }
            this.condominioListUnFiltered = condominioList
              .filter(condominio => condominio.sottoTipoCluster === this.condominioFieldValue)
              .sort((a, b) => a.customerId - b.customerId);
            this.condominioListIsFiltered = this.condominioListUnFiltered.filter(condominio => condominio.moroso);
            this.condominioList = this.condominioListUnFiltered;
            this.isShowSpinner = false;
          });
      }
    });
    this.formGroupYearReport = this.formBuilder.group({
      year: [this.defaultYear, Validators.required]
    });
  }

  doFilter() {
    if (this.isFiltered) {
      this.isFiltered = false;
      this.btnFilter = this.FILTER_UNPAID_BILLS_TEXT;
      this.condominioList = this.condominioListUnFiltered;
    } else {
      if (this.condominioListIsFiltered.length !== 0) {
        this.isFiltered = true;
        this.btnFilter = this.FILTER_ALL_TEXT;
        this.condominioList = this.condominioListIsFiltered;
      } else {
        this.dialogModalActions.showDialogModal(successModalNoCondominiumsWithUnpaidBills);
      }
    }
  }

  randomImage(index: number) {
    index++;
    if (index % 7 !== 0) {
      return index % 7;
    } else {
      return 7;
    }
  }

  openNewUser(customerId) {
    localStorage.setItem('clientId', customerId);
    this.router.navigate(['/home/<USER>']);
  }

  private fillNotificationMap(clientNotification: ClientNotification) {
    const key = parseInt(clientNotification.clientId, 10);
    if (this.allNotificationsForClient.has(key)) {
      const clientNotificationList = this.allNotificationsForClient.get(key);
      clientNotificationList.push(clientNotification);
      this.allNotificationsForClient.set(key, clientNotificationList);
    } else {
      const array = new Array<ClientNotification>();
      array.push(clientNotification);
      this.allNotificationsForClient.set(key, array);
    }
  }

  getClientNotificationsForClient(clientId: number) {
    const clientNotificationList = this.allNotificationsForClient.get(clientId);
    return clientNotificationList || new Array<ClientNotification>();
  }

  isExistNotificationsForUser(clientId: number) {
    const clientNotificationList = this.allNotificationsForClient.get(clientId);
    return !!clientNotificationList;
  }

  downloadExcelFile() {
    if (this.formGroupYearReport.valid) {
      this.isShowSpinner = true;
      this.userDataService.getUserCondominioFiscalCode(localStorage.getItem('clientId')).subscribe(information => {
        this.excelFileService.getCondominiExcelFile(this.formGroupYearReport.value.year, information.response[0].CfPivaAmministratore).subscribe(response => {
          const blob = new Blob([response], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
          const data = window.URL.createObjectURL(blob);
          const file = document.createElement('a');
          file.download = `RendicontazioneCondomini_${this.formGroupYearReport.value.year}.xlsx`;
          file.href = data;
          document.body.appendChild(file);
          file.click();
          document.body.removeChild(file);
          this.isShowSpinner = false;
        }, () => {
          this.showNotFindFileWindow = true;
          this.isShowSpinner = false;
        });
      });
    }
  }

  hideDialogModal() {
    this.showNotFindFileWindow = false;
  }
}
