import {NgModule} from '@angular/core';
import {CondominioComponent} from './condominio.component';
import {RouterModule, Routes} from '@angular/router';
import {LayoutModule} from '../../layout/layout.module';
import {CondominioLoadGuard} from '../../services/condominioLoad.guard';
import {CommonModule} from '../../common/common.module';
import {SharedModule} from '../../shared/shared.module';
import {SpinnerAction} from '../../redux/spinner/actions';

const routes: Routes = [
  {path: '', component: CondominioComponent}
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    LayoutModule,
    CommonModule,
    SharedModule
  ],
  declarations: [
    CondominioComponent
  ],
  exports: [
    RouterModule
  ],
  providers: [
    CondominioLoadGuard,
    SpinnerAction
  ]
})
export class CondominioModule {
}
