export class Coupon {
  couponCode: string;
  couponType: CouponTypes;
  receivingDate: string;
  expiryDate: string;
  awardDate: string;
  numTarget: number;
  numUses: number;
  numComplete: number;
}

export class Subscription {
  subscriptionId: number;
  msisdnid: number;
  isAvailableOptimaYoung: boolean;
  coupons: Coupon[];

  // MGM fields
  isEligibleMGM?: boolean;
  couponCode?: string;
  couponType?: string;
  expiryDate?: string;
  pod?: string;
  contractId?: number;
  receivingDate?: string;
  invoiceNumber?: number;
  used?: boolean;
  fileName?: string;
}

export class OptimaYoung {
  clientId: number;
  subscriptions: Subscription[];
}

export enum CouponTypes {
  LEVEL_1 = 'OPT_CAMPUS_LIV_1',
  LEVEL_2 = 'OPT_CAMPUS_LIV_2',
  LEVEL_3 = 'OPT_CAMPUS_LIV_3',
  LUCE_OPTIMA = 'MEMBER_GET_MEMBER'
}
