import { Component, ViewChild, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import { JwtHelperService } from '@auth0/angular-jwt';
import {ChatActions} from '../../redux/chat/actions';
const helper = new JwtHelperService();

@Component({
  selector: 'app-assistant',
  templateUrl: './assistant.component.html',
  styleUrls: ['./assistant.component.scss'],
})


export class AssistantComponent implements AfterViewInit, OnDestroy {
  @ViewChild('pop') pop: any;
  isConsumer = false;
  userInfo: any = {
    fax: ' 800.93.93.92',
    email: '<EMAIL>'
  };

  constructor(private chatActions: ChatActions) {
    const data = helper.decodeToken(localStorage.getItem('access_token'));
    const isConsumer = data && data.consumer;
    this.initData(isConsumer);
    window.scrollTo(0, 0);
  }

  initData(isConsumer: any) {
    this.isConsumer = isConsumer;
    if (isConsumer) {
      this.userInfo = {
        fax: '800.95.96.00',
        email: '<EMAIL>'
      };
    }
  }

  open(url) {
    window.open(url, '_blank');
  }

  showChat() {
    this.chatActions.showChat();
  }

  ngAfterViewInit() {
    const that = this;
    // added event listener touchstart on body to order close popup. (iOS)
    document.body.addEventListener('touchstart', function (e) { if (that && that.pop) { that.pop.hide(); }}, false);
    if (document.querySelector('#myPop')) {
      document.querySelector('#myPop').addEventListener('click', function (e) {
        if (that && that.pop) { that.pop.show(); }
        e.stopPropagation();
      }, true);
    }
  }
  ngOnDestroy(): void {
    document.body.removeEventListener('touchstart',  function (e) {});
    if (document.querySelector('#myPop')) {
      document.querySelector('#myPop').removeEventListener('click', function (e) {});
    }
  }
}
