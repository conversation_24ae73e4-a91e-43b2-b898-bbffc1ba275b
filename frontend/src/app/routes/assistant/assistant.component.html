<div
  class="col-lg-10 col-lg-offset-1 col-md-10 col-md-offset-1 col-sm-12 col-xs-12 assistant-layout">
  <div class="col-lg-12 col-md-12 col-sm-12 assistant-header">
    Assistenza Clienti Optima
  </div>
  <div class="col-lg-12 col-md-12 col-sm-12 appeal-block clearfix">
    <div class="col-lg-12 col-md-12 appeal-image">
        <div class="alan-ellen-image"></div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 optima-info-block no-padding">

      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding ask-alan-block">
        <div class="col-lg-12 col-md-12 no-padding alan-label">
          <div class="info-title">Chiedi subito assistenza:</div>
        </div>
      </div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding
         info-block">
      <div class="col-lg-3 col-md-3 no-padding dark-border info-label"
           (click)="open('https://assistenza.optimaitalia.com')">
        <div class="app-icon colibri-icon"></div>
        <div class="info-title">Sul nostro sito Web</div>
        <div class="info-description">assistenza.optimaitalia.com</div>
      </div>
      <div class="col-lg-3 col-md-3 no-padding dark-border info-label"
           (click)="open('https://www.facebook.com/optimaitalia')">
        <div class="app-icon app-facebook"></div>
        <div class="info-title">Su Facebook</div>
        <div class="info-description">facebook.com/optimaitalia</div>
      </div>
      <div class="col-lg-3 col-md-3 no-padding dark-border info-label"
           (click)="open('http://telegram.me/optima_italiabot')">
        <div class="app-icon app-telegram"></div>
        <div class="info-title">Su Telegram</div>
        <div class="info-description">Optima Italia</div>
      </div>
      <!--<div class="col-lg-3 col-md-3 no-padding dark-border info-label"
           (click)="open('https://assistant.google.com/services/a/uid/000000b59a8e13e7?hl=it_it')">
        <div class="app-icon app-google-assistant"></div>
        <div class="info-title">Su Google Assistant</div>
        <div class="info-description">Optima Italia</div>
      </div>
      <div class="col-lg-3 col-md-3 no-padding dark-border info-label"
           (click)="open('https://www.amazon.it/Optima-Italia-S-p-a-assistenza-optima/dp/B0848J4QTZ')">
        <div class="app-icon app-amazon-alexa"></div>
        <div class="info-title">Su Amazon Alexa</div>
        <div class="info-description">Optima Italia</div>
      </div>-->
    </div>
  </div>
  <div class="col-lg-12 col-md-12 assistant-footer">
    <!--<button (click)="showChat()">Chatlive</button>-->
    <!--<button container="body" #pop="bs-popover" id="myPop" type="button" class="numeri-button"-->
            <!--placement="top" triggers="focus" [popover]="popTemplate">-->
      <!--Numeri utili-->
    <!--</button>-->
    <!--<button-->
      <!--(click)="open(isConsumer?'mailto:<EMAIL>'-->
        <!--:'mailto:<EMAIL>')">-->
      <!--E-mail-->
    <!--</button>-->
  </div>
</div>


<ng-template #popTemplate>
  <div>
    <div class="numero-block">
      <div>Numero Verde: <span class="text-bold">800.91.38.38</span></div>
      <div>Fax Verde: <span class="text-bold">{{userInfo.fax}}</span></div>
    </div>
    <div>
      <div class="text-bold"><i>Il servizio è disponibile dal lundì al venerdì,</i></div>
      <div class="font-italic"><i>dalle ore 08:30 alle ore 21:30.</i></div>
    </div>
  </div>
</ng-template>
