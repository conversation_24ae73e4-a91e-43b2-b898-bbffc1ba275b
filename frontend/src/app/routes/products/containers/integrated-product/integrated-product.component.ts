import {Component, OnD<PERSON>roy} from '@angular/core';
import {ActivatedRoute, Params} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import {UserData} from '../../../../common/model/userData.model';
import {Cluster} from '../../../../common/enum/Cluster';
import {EmailService} from '../../../../common/services/email/email.service';
import {NotificationService} from '../../../../common/services/notification/notification.service';
import {messages} from '../config/config';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {IncidentEventResponse} from '../../../autolettura/model/IncidentEventResponse';

@Component({
  selector: 'app-integrated-product',
  templateUrl: './integrated-product.component.html',
  styleUrls: ['./integrated-product.component.scss']
})
export class IntegratedProductComponent implements OnDestroy {

  @select(['user'])
  userInfo: Observable<any>;

  userData: UserData;

  hasActiveOffers: boolean;

  routerParams: Observable<Params>;

  userInfoSubscription: Subscription;

  constructor(private activatedRoute: ActivatedRoute, private mailService: EmailService,
              private notificationService: NotificationService, private incidentEventService: IncidentEventService) {
    this.routerParams = activatedRoute.params;
    this.userInfoSubscription = this.userInfo.subscribe(userData => {
      this.userData = userData.userInfo;
      this.hasActiveOffers = userData.hasActiveOffers;
    });
  }

  downloadDocument() {
    if (this.userData.cluster && this.userData.cluster.value === Cluster.BUSINESS) {
      window.open('/assets/documents/ModuloAggUtenzaServizioBU.PDF', '_blank');
    } else {
      window.open('/assets/documents/ModuloAggUtenzaServizioCO.PDF', '_blank');
    }
  }

  createIncidentEvent(): Observable<IncidentEventResponse> {
    return this.hasActiveOffers ? this.incidentEventService.remodulationProductRequest()
      : this.incidentEventService.activateNewProductRequest();
  }


  sendIntegratedSolutionRequest() {
    this.createIncidentEvent()
      .subscribe((response: IncidentEventResponse) =>
          this.incidentEventService.createIncidentEventResponseNotification(response, messages.successMessage),
        () => this.notificationService.errorMessage(messages.failedMessage));
  }



  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.userInfoSubscription]);
  }

}
