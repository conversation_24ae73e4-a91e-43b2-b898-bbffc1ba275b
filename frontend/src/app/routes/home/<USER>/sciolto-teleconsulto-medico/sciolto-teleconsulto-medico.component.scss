@import "../../../../shared/styles/colors";
@import "../../../../shared/styles/app.mat-dropdown";
@import "../../../home/<USER>/mobile-layout/mobile-layout.component";

.titleBlock {
  border-radius: 10px;

  .title {
    font-weight: bold;
  }

  .doctor-picture {
    position: absolute;
    top: 7px;
    left: 18px;
  }
}

.block {
  border-radius: 10px;
  border: 1px solid $menu-border;
  background-color: #f0f5f9;
  height: auto;
  padding: 15px;
  color: #36749d;
  min-height: 200px;
  overflow-y: auto;
}

.flex-block {
  display: flex;
  height: 265px;

  .icon-banner {
    background-size: contain;
    width: 270px;
  }

  .banner-text {
    font-size: 25px;
    padding: 30px;
  }

}

.additional-text {
  font-size: 21px;
  font-weight: bold;
  font-style: italic;
  margin: 35px;
}

.promo-block {
  background-color: white;
  padding: 20px;
  font-size: 20px;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;

  .flex-promo-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .promo-icon {
      width: 38px;
      margin-right: 15px;
    }
  }
}

.small-text {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 5px;
}

.btn-success {
  display: block;
  border-radius: 19px;
  margin: auto;
  font-size: 15px;
  padding-left: 25px;
  padding-right: 25px;
  background-color: $green;
}

@media only screen and (max-width: 1335px) {
  .flex-block {
    .banner-text {
      font-size: 22px;
    }
  }
  .additional-text {
    font-size: 18px;
  }
  .promo-block {
    font-size: 17px;
  }
  .btn-success {
    font-size: 14px;
  }
}

@media only screen and (max-width: 1190px) {
  .additional-text {
    margin-top: 90px;
  }
}

@media only screen and (max-width: 1090px) {
  .flex-block {
    .banner-text {
      font-size: 20px;
    }

    .icon-banner {
      width: 250px;
    }
  }
  .additional-text {
    font-size: 16px;
    margin-top: 100px;
  }
  .promo-block {
    font-size: 15px;
  }
  .block {
    padding: 5px;
  }
}

@media only screen and (max-width: 991px) {
  .flex-block {
    .icon-banner {
      width: 270px;
    }
  }
  .additional-text {
    margin-top: 50px;
  }
  .block {
    padding: 15px;
  }
}

@media only screen and (max-width: 720px) {
  .flex-block {
    .banner-text {
      font-size: 19px;
    }

    .icon-banner {
      width: 260px;
    }
  }
  .additional-text {
    font-size: 17px;
    margin-top: 75px;
  }
  .promo-block {
    font-size: 15px;
  }
  .block {
    padding: 5px;
  }
}

@media only screen and (max-width: 640px) {
  .flex-block {
    .banner-text {
      font-size: 18px;
    }

    .icon-banner {
      width: 250px;
    }
  }
  .additional-text {
    font-size: 16px;
    margin-top: 95px;
  }
  .btn-success {
    font-size: 13px;
  }
}

@media only screen and (max-width: 600px) {
  .flex-block {
    .icon-banner {
      display: none;
    }
  }
  .additional-text {
    margin-top: 0;
  }
}

@media only screen and (max-width: 420px) {
  .additional-text {
    margin-top: 70px;
  }
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 315px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #B0C7DD;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 680px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 30px;
  font-size: 24px;
}
