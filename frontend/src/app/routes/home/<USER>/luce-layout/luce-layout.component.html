<div class="col-md-11 titleBlock">
  <div class="icon">
  </div>
  <div class="title">
    CONTROLLA STATO E DETTAGLIO DEI TUOI CONSUMI LUCE
  </div>
</div>
<div class="col-md-12 contentBlock">
  <div class="col-md-5 block main {{detail?'blockWithDetail':''}} no-padding-mobile">
    <div *ngFor="let service of sortedServices">
      <div *ngFor="let utility of service.utilities; let i = index" class="lineBlock">
        <div class="{{!detail?'subBlock':'subBlockWithDetails'}} plainBlock
          {{( detail && (utility.id == detail.id)) ?'active':'noActive'}}">
          <p><b>Stato: </b>{{utility.status  | titlecase}}</p>
          <p><b>Pod: </b>{{utility.utNumber}}</p>
          <div *ngIf="showEtichetta" class="etichetta-field" [style.height]="
               (etichettaFormGroups[i].controls['etichetta'].dirty || etichettaFormGroups[i].controls['etichetta'].touched)
            && (etichettaFormGroups[i].controls['etichetta'].hasError('required')
            ||  etichettaFormGroups[i].controls['etichetta'].hasError('minlength')
            ||  etichettaFormGroups[i].controls['etichetta'].hasError('maxlength')) ? '30px' : '15px'">
            <b>Etichetta: </b>
            <div class="user-data-editor">
              <app-user-data-editor [formGroup]="etichettaFormGroups[i]" name="etichetta"
                                    (editFieldsEventEmitter)="checkForEditingFields($event,
                                    utility.utNumber,
                                    etichettaFormGroups[i].controls['etichetta'].value,
                                    getEtichettaLabelByUtNumber(utility.utNumber))"
                                    [onAccept]="changeEtichetta()"
                                    [modifiable]="true" [emptyOnEdit]="false"
                                    placeholder="Inserisci l'etichetta">
                <confirm-message>
                <span class="confirm-message">
                  L’etichetta inserita è
                  <b>“{{etichettaFormGroups[i].controls['etichetta'].value}}”</b>
                  ,per completare l’operazione clicca su "Conferma".
                </span>
                </confirm-message>
              </app-user-data-editor>
            </div>
          </div>
          <p class="addition"
             *ngIf="podDetails&&podDetails[utility.utNumber]">
            <b>Indirizzo: </b>
            {{podDetails[utility.utNumber].sedeOperativa}}</p>

          <div *ngIf="utility.status==='ATTIVATO'">
            <div *ngIf="!(detail && (utility.id == detail.id))"
                 class="button" (click)="selectValue(utility, i)">Dettaglio
            </div>
            <div *ngIf="(detail && (utility.id == detail.id))" class="button nascondi" (click)="hide()">Nascondi</div>
          </div>
        </div>
        <ng-container *ngIf="checkid == i">
          <div class="visible-sm-block visible-xs-block">
            <ng-container *ngTemplateOutlet="luceDetails"></ng-container>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="hidden-sm hidden-xs">
    <ng-container *ngTemplateOutlet="luceDetails"></ng-container>
  </div>
</div>


<ng-template #luceDetails>
  <div *ngIf="detail" class="col-md-6 block detail">
    <div class="col-md-12 no-padding-mobile">
      <div class="desktopView">

        <span class="detailRow bigLetter"> <b>Pod: {{detail.utNumber}}</b> </span>

        <span class="detailRow smallLetter">  <b>Stato: </b>{{detail.status  | titlecase}}</span>

      </div>
      <span class="detailRow smallLetter">
        <b>Data Attivazione: </b>{{detail.startDate| date : "dd/MM/y"}}</span>
      <div *ngIf="podDetails&&podDetails[detail.utNumber]">
        <!--<span class="detailRow"><b>Tipologia contratto: </b>{{podDetails[0].tipoUso}}</span>-->
        <span class="detailRow smallLetter">
          <b>Potenza contatore: </b>{{podDetails[detail.utNumber].potDisp}} Kw</span>
        <span *ngIf="podDetails[detail.utNumber].tipoUso" class="detailRow smallLetter">
          <b>Tipologia d'uso: </b>{{podDetails[detail.utNumber].tipoUso}}</span>
        <span class="detailRow smallLetter">
          <b>Indirizzo fornitura: </b>{{podDetails[detail.utNumber].sedeOperativa }}</span>
        <span class="detailRow smallLetter">
          <b>Tipo Contatore: </b>{{podDetails[detail.utNumber].tipo}}</span>
      </div>
    </div>
  </div>

  <div class="col-md-1 icons" *ngIf="detail">

    <a class="icon-autolettura" [routerLink]="['/faidate/autolettura', 'luce']">
      <div class="icon autolettura"></div>
    </a>

    <section class="icon-selection" *ngIf="pdf">
      <a class="app--btn-dropdown no-hover" [matMenuTriggerFor]="menu">
        <div class="icon modifica"></div>
      </a>
      <mat-menu #menu="matMenu" xPosition="before" yPosition="below">
        <div class="mat-menu-style">
          <button mat-menu-item *ngIf="pdf.length===0">
            <span> No PDF </span>
          </button>
<!--          <button class="red" mat-menu-item *ngIf="pdf.length>0">-->
<!--            Variazioni e richieste-->
<!--          </button>-->
          <button class="menu-button odds-bg" mat-menu-item *ngFor="let pdfRow of pdf">
            <span class="icon-pdf-load"> </span>
            <a target='_blank' href="{{pdfRow.link}}">{{pdfRow.name}}</a>
          </button>
        </div>
      </mat-menu>
    </section>
  </div>

  <div *ngIf="detail" class="col-lg-11 col-md-11 service-chart-block">
    <div class="col-lg-5 col-md-5 description-block clearfix">
      <div class="labels container">
       <div class="subTitle">CONSUMI MENSILI DELL'ANNO IN CORSO</div>
      <app-info>
        <info-button><i class="info-circle">i</i></info-button>
        <info-message> Il grafico riporta i consumi reali rilevati sul tuo contatore per i mesi di riferimento
        </info-message>
      </app-info>
      <div *ngIf="!showConsumiDelInCorso" class="button button-position" (click)="showModalWindow()">SCARICA IL REPORT</div></div>
      <div *ngIf="offers&&offers.length&&consumptionAnnotation>-1"
           class="col-lg-12 col-sm-12 col-xs-12 no-padding special-font">
        <span>Il consumo previsto dal tuo Tutto-In-Uno per questo punto è {{consumptionAnnotation}} kwh/mese.</span>
      </div>
    </div>
    <div *ngIf="!showConsumiDelInCorso" class="legend">
      <app-info>
        <info-button>
          <div class="legend-color tuoi-consumi"></div>
        </info-button>
        <info-message>kWh</info-message>
      </app-info>
      <span class="chart-dot-text special-font-consumi">Consumi reali fatturati</span>
      <app-info>
        <info-button>
          <div class="legend-color consumi-del-in-corso"></div>
        </info-button>
        <info-message>kWh</info-message>
      </app-info>
      <span class="chart-dot-text special-font-consumi">Consumi del mese in corso</span>
    </div>
    <div *ngIf="showConsumiDelInCorso" class="legend">
      <app-info>
        <info-button>
          <div class="legend-color tuoi-consumi"></div>
        </info-button>
        <info-message>kWh</info-message>
      </app-info>
      <span class="chart-dot-text special-font-consumi-without-corso">Consumi reali fatturati</span>
    </div>

    <div class="col-lg-7 col-md-7 service-chart clearfix">
      <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 ">
    <span *ngIf="chartPaginator&&chartPaginator.hasNext" class="fa fa-angle-left pagination-nav"
          [title]="chartPaginator.nextValue()"
          (click)="chartPaginator.next()"></span>
      </div>
      <div *ngIf="!hasAdjustments" class="col-lg-10 col-md-10 col-sm-10 col-xs-10  text-align-middle">Non risultano
        conguagli
        emessi.
      </div>
      <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1  right">
    <span *ngIf="chartPaginator&&chartPaginator.hasPrevious" class="fa fa-angle-right pagination-nav right"
          [title]="chartPaginator.previousValue()"
          (click)="chartPaginator.previous()"></span>
      </div>
      <div class="chart-size-by-month">
        <app-chart [config]="chartConfig"></app-chart></div>
      <div *ngIf="!showConsumiDelInCorso" class="container-warning-circle-by-days">
        <div class ="warning-circle-by-days">!</div>
        <div class="special-font-text-by-days">Clicca sul mese in corso per consultare i consumi giornalieri</div>
      </div>
      <div class="container-arrow" *ngIf="detailsByDay" (click)="hideDetailsByDay()">
        <div class="arrow-size">&#xfe3f;</div>
      <div class="secondTitle">CONSUMI GIORNALIERI DEL [{{selectedDate}}/{{currentYear}}]</div>
      </div>
      <div class="spanTitle" *ngIf="hasDetailsByDay">Non risultano dettagli per il mese selezionato</div>
        <div class="chart-size-by-days">
        <app-chart *ngIf="detailsByDay" [config]="chartConfigByDays"></app-chart></div>
      <div *ngIf="detailsByDay" class="container-warning-circle">
        <div class ="warning-circle">!</div>
        <div class="special-font-text">Clicca sul singolo giorno per consultare il tuo consumo orario</div>
      </div>
        <div *ngIf="detailsByDay" class="text-information">Gentile Cliente, i consumi indicati potrebbero essere aggiornati o rettificati da parte del Distributore Locale</div>
      <div *ngIf="detailsByDay && showAdditionalInfo" class="text-information">Inoltre, ti ricordiamo che i valori potrebbero differire anche in base al tuo coefficiente K, fattore moltiplicativo.</div>
      <div *ngIf="detailsByHours" class="conainer-third-title" (click)="hideDetailsByHours()">
          <div class="arrow-third-size">&#xfe3f;</div>
        <div class="thirdTitle">CONSUMO ORARIO</div>
        </div>
      <div *ngIf="detailsByHours" class="chart-position">
        <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 ">
          <span *ngIf="detailsByHours && showLeftArrow" class="fa fa-angle-left pagination-nav-hours-left" (click)="previousPage()">
          </span>
        </div>
        <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1  right">
    <span *ngIf="detailsByHours && showRightArrow" class="fa fa-angle-right pagination-nav-hours-right right" (click)="nextPage()"></span>
        </div>
      <app-chart [config]="chartConfigByHours"></app-chart></div>
      <div *ngIf="detailsByHours" class="text-information">Gentile Cliente, i consumi indicati potrebbero essere aggiornati o rettificati da parte del Distributore Locale</div>
      <div *ngIf="detailsByHours && showAdditionalInfo" class="text-information">Inoltre, ti ricordiamo che i valori potrebbero differire anche in base al tuo coefficiente K, fattore moltiplicativo.</div>
    </div>
    </div>
</ng-template>
<div class="modal-div display" *ngIf="ModalWindow">
  <div class="inner-modal-div" [formGroup]="formExelData">
    <i class="fa fa-times" aria-hidden="true" (click)="hideDialogModal()"></i>
    <div class="modal-text">Per scaricare un report dettagliato con i tuoi consumi inserisci il periodo di riferimento.</div>
    <div class="container-flex-text">
      <div class="text-label">Da</div>
      <input class="input-data" type="date" placeholder="GG/MM/AAAA" formControlName="Da" max="{{dateRestrictions}}">
    </div>
    <small class="error"  *ngIf="formExelData.controls['Da'].hasError('required')
            && (formExelData.controls['Da'].dirty || formExelData.controls['Da'].touched)">
      Scegliere una data valida
    </small>
    <div class="container-flex-text">
      <div class="text-label">A</div>
      <input class="input-data fix-position" type="date" placeholder="GG/MM/AAAA" formControlName="A" max="{{dateRestrictions}}">
    </div>
    <small class="error position-error"  *ngIf="formExelData.controls['A'].hasError('required')
            && (formExelData.controls['A'].dirty || formExelData.controls['A'].touched)">
      Scegliere una data valida
    </small>
    <button class="modal-button" (click)="downloadFile()">SCARICA</button>
  </div>
</div>
