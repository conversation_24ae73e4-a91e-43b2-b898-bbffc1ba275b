import {Injectable} from '@angular/core';
import * as info from '../../../../routes/fai-da-te/PDFservice.json';

class PdfLink {
  constructor(name: string, url: string) {
    this.name = name;
    this.link = url;
  }

  name: string;
  link: string;
}

@Injectable()
export class HomeService {
  servicesMap = {
    ENERGIA: 'EE', // EE
    ADSL: 'ADSL',
    VOCE: 'VOCE',
    WLR: 'VOCE',
    VOIP: 'VOCE',
    GAS: 'GAS',
    MOBILE: 'MOBILE',
  };
  claster = {
    CONSUMER: 'CO',
    BUSINESS: 'BU'
  };
  pdf: Array<PdfLink> = [];

  getPDFList(serviceName, userCluster) {
    const cluster = this.claster[userCluster];
    if (info[this.servicesMap[serviceName]]) {
      return info[this.servicesMap[serviceName]][cluster];
    }
    else {
      return null;
    }
  }
}
