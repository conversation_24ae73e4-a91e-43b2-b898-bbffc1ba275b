@import "../../../../shared/styles/colors";
@import "../../../../shared/styles/app.mat-dropdown";
@import "../../../home/<USER>/gas-layout/gas-layout.component";

.titleBlock {
  border-radius: 10px;

  .title {
    font-weight: bold;
  }
}

.block {
  border-radius: 10px;
  border: 1px solid $menu-border;
  background-color: #f0f5f9;
  height: auto;
}

.flex-block {
  display: flex;
  height: 265px;

  .icon-banner {
    background-size: contain;
    width: 270px;
  }

  .banner-text {
    font-size: 27px;
    padding: 30px;
  }

  .additional-text {
    font-size: 16px;
  }
}

.promo-block {
  background-color: white;
  padding: 20px;
  font-size: 20px;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;

  .flex-promo-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .promo-icon {
      width: 38px;
      margin-right: 15px;
    }
  }
}

.btn-success {
  display: block;
  border-radius: 19px;
  margin: 5px auto auto;
  font-size: 15px;
  padding-left: 25px;
  padding-right: 25px;
  background-color: $green;
}

.small-text {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 5px;
}

@media only screen and (max-width: 1180px) {
  .promo-block {
    font-size: 18px;
  }
  .flex-block {
    .banner-text {
      font-size: 23px;
    }
  }
  .btn-success {
    font-size: 14px;
  }
}

@media only screen and (max-width: 650px) {
  .promo-block {
    font-size: 15px;
  }
  .flex-block {
    .banner-text {
      font-size: 18px;
    }
  }
  .btn-success {
    font-size: 13px;
  }
}

@media only screen and (max-width: 550px) {
  .flex-block {
    .icon-banner {
      display: none;
    }

    .banner-text {
      font-size: 25px;
    }
  }
}

@media only screen and (max-width: 375px) {
  .flex-block {
    .banner-text {
      font-size: 20px;
    }
  }
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 315px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #B0C7DD;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 680px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 30px;
  font-size: 24px;
}
