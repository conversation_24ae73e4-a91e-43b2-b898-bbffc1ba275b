import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {MenuService} from '../../../../core/menu/menu.service';
import {UserServicesService} from '../../../profilePage/userServices/userServices.service';
import {ChatActions} from '../../../../redux/chat/actions';
import {Cluster} from '../../../../common/enum/Cluster';
import {Subscription} from 'rxjs/Subscription';
import {UserData} from '../../../../common/model/userData.model';
import {AmazonPrimeService} from '../../../../common/services/amazonprime/amazon-prime.service';
import {DialogModalEntity} from '../../../../common/model/dialogModal/DialogModalEntity';
import {Router} from '@angular/router';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';

@Component({
  selector: 'app-index-layout',
  templateUrl: './index-layout.component.html',
  styleUrls: ['./index-layout.component.scss']
})
export class IndexLayoutComponent implements OnInit, OnDestroy {
  @select(['services', 'services'])
  services: Observable<Array<any>>;
  @select(['services', 'activeServices'])
  serviceData: Observable<object>;
  ///
  @select(['user', 'clientOffers'])
  private clientOffers;

  @select(['user', 'userInfo'])
  userInfoRedux: Observable<UserData>;

  hasTutoInUnoActive: boolean;
  businessClusterSubscription: Subscription;
  isBusinessCluster: boolean;
  isBollettinoPayment: boolean;
  isCartaceaType: boolean;
  amazonStatus: boolean;
  old: boolean;
  ///
  activationUrl: string;
  menuItems: Array<any>;
  isMobile = window.innerWidth <= 991;
  public notification = {
    messages: [
      {
        them: 'them',
        text: 'text'
      },
      {
        them: 'them',
        text: 'text'
      }
    ],
    documents: [
      {
        them: 'them',
        text: 'text'
      }
    ]
  };

  constructor(
    private chatActions: ChatActions,
    private userServicesService: UserServicesService,
    private menu: MenuService,
    private amazonprimeService: AmazonPrimeService,
    private router: Router,
    private dialogModalActions: DialogModalActions
  ) {
    this.menuItems = this.menu.getSideMenu();

    this.services.subscribe((data: any) => {
      const activeServices = this.userServicesService.getActiveServices(data);
      Object.keys(activeServices).map((key) => {
        this.menuItems[0].display = true;
      });
    });

    this.serviceData.subscribe((data) => {
      Object.keys(data).map((key) => {
        if (key === 'ENERGIA' || key === 'ELETTRICITA' || key === 'GAS') {
          this.menuItems[0].display = true;
        }
      });
    });

    this.clientOffers.subscribe(offersArray => {
      this.hasTutoInUnoActive = offersArray.filter(value => value.adsl || value.ee || value.gas
        || value.voce).length > 0;
    });

    this.businessClusterSubscription = this.userInfoRedux.subscribe(data => {
      if (data) {
        if (data.cluster) {
          this.isBusinessCluster = data.cluster.value === Cluster.BUSINESS;
        }
        this.isBollettinoPayment = data.paymentData.modalitaPagamento === 'Bollettino postale';
        this.isCartaceaType = data.billingType === 'Cartacea';
      }
    });

    this.amazonprimeService.getAmazonPrimeData(localStorage.getItem('clientId'))
      .subscribe(amazonInfo => {
        this.amazonStatus = amazonInfo.esito;
        this.old = amazonInfo.old;
        this.activationUrl = amazonInfo.activationUrl;
      });
  }

  redirectToAmazon() {
    this.amazonprimeService.getAmazonPrimeData(localStorage.getItem('clientId'))
      .subscribe(res => {
        if (res.descrizioneEsito === 'KO') {
          this.dialogModalActions.showDialogModal({
            title: 'Attenzione, si è verificato un errore',
            img: '/assets/img/icons/error.png'
          } as DialogModalEntity);
        }
        if (res.activationUrl === null || res.activationUrl === 'null') {
          this.dialogModalActions.showDialogModal({
            title: 'Attenzione, si è verificato un errore',
            img: '/assets/img/icons/error.png'
          } as DialogModalEntity);
        } else {
          window.open(res.activationUrl, '_blank');
          this.router.navigate(['/home/<USER>']);
        }
      });
  }

  redirectToAmazonForOld() {
    window.open(this.activationUrl, '_blank');
  }

  // createAmazonSubscriber() {
  //   this.amazonprimeService.createAmazonPrimeSubscriber(localStorage.getItem('clientId')).subscribe(res => {
  //     if (res.descrizioneEsito === 'KO') {
  //       this.dialogModalActions
  //         .showDialogModal({
  //           title: 'Attenzione, si è verificato un errore',
  //           img: '/assets/img/icons/error.png'
  //         } as DialogModalEntity);
  //     } else {
  //       this.amazonprimeService.createAmazonPrimeSubscription(localStorage.getItem('clientId'))
  //         .subscribe(res => {
  //           if (res.descrizioneEsito === 'KO') {
  //             this.dialogModalActions.showDialogModal({
  //               title: 'Attenzione, si è verificato un errore',
  //               img: '/assets/img/icons/error.png'
  //             } as DialogModalEntity);
  //           }
  //           if (res.activationUrl === null || res.activationUrl == 'null') {
  //             this.dialogModalActions.showDialogModal({
  //               title: 'Attenzione, si è verificato un errore',
  //               img: '/assets/img/icons/error.png'
  //             } as DialogModalEntity);
  //           } else {
  //             window.open(res.activationUrl, '_blank');
  //             this.router.navigate(['/home/<USER>'])
  //           }
  //         });
  //     }
  //   });
  // }

  openChat() {
    this.chatActions.showChat();
  }

  scroll() {
    window.scrollTo(0, 0);
  }

  ngOnInit() {
  }

  ngOnDestroy(): void {
    this.businessClusterSubscription.unsubscribe();
  }

}
