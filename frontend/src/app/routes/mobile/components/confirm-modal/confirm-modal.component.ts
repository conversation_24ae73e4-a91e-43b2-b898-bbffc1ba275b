import { Component, Input, OnInit, ViewChild } from '@angular/core';
import ConfirmModalOption from '../../../../common/model/mobile/ConfirmModalOption';

@Component({
  selector: 'app-confirm-modal',
  templateUrl: './confirm-modal.component.html',
  styleUrls: ['./confirm-modal.component.scss']
})
export class ConfirmModalComponent implements OnInit {

  @ViewChild('classicModal') classicModal: any;

  option: ConfirmModalOption;

  constructor() {
  }

  ngOnInit() {
  }

  @Input('option')
  set openModalWindow(option: ConfirmModalOption) {
    if (option) {
      this.option = option;
      this.classicModal.show();
    }
  }

  confirm() {
    if (this.option.onSubmit) {
      this.option.onSubmit();
    }
    if (this.option.closeOnConfirm) {
      this.classicModal.hide();
    }
  }

  closeDialog() {
    if (this.option && this.option.onClose) {
      this.option.onClose();
    }
    this.classicModal.hide();
  }

}
