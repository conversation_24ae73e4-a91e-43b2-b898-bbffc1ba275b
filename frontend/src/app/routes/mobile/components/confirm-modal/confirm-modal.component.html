<div class="modal fade" bsModal #classicModal="bs-modal" tabindex="-1" role="dialog"
     aria-labelledby="mySmallModalLabel"
     aria-hidden="true">
  <div class="modal-dialog modal-xs">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="closeDialog()">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">
          <ng-content select="modal-title"></ng-content>
        </h4>
      </div>
      <div class="modal-body">
        <p class="modal-suggestion">
          <ng-content select="modal-suggestion"></ng-content>
        </p>
        <p *ngIf="option && option.serviceName" class="service-name">{{option.serviceName}}</p>
        <p>
          <ng-content select="modal-description"></ng-content>
        </p>
      </div>
      <div class="modal-footer" select="modal-footer">
        <div  *ngIf="option && option.showConfirmButtons">
          <button class="button" (click)="confirm()">CONFERMA</button>
          <button (click)="closeDialog()" class="button">ANNULLA</button>
        </div>
      </div>
    </div>
  </div>
</div>
