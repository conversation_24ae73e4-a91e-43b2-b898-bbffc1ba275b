export default class SubscriptionAutoricaricaRequest {
  SUBSCRIPTION_ID: string;
  COD_CLIENTE: string;
}

export class AutoricaricaInformationResponse {
  SubscriptionId: string;
  CodCLiente: string;
  DataAttivazione: string;
  Brand: string;
  ExpireMonth: number;
  ExpireYear: number;
  CardExpiration: string;
  MaskedPan: string;
  RicaricaRicorrente: number;
  importo: number;
  EsitoResponse: EsitoResponse;
  // PayPal
  ProviderPagamento: string;
  EMail: string;
}

export class EsitoResponse {
  Descrizione: string;
  Esito: string;
}

export class DeactivateOrActivateAutoRicaricaRequest {
  idCliente: string;
  utenza: string;
  origine: string;

  constructor(utenza: string) {
    this.idCliente = localStorage.getItem('clientId');
    this.utenza = utenza;
    this.origine = 'selfcare';
  }
}
