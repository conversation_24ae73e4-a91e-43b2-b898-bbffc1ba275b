@import "src/app/shared/styles/colors";

label {
  color: $dark-blue;
  font-weight: normal;
  margin: 0;
  width: 50%;
}

option {
  font-weight: bold;
}


input[type="text"]#simNumberTemplate {
  width: 50px;
  text-align: right;
}

input[type="text"]#simNumber {
  outline: none;
}

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.flex-panel {
  display: flex;

  .left-part {
    width: 50%;
    padding: 2%;

    .importo-size-img {
      width: 80px;
      cursor: pointer;
    }

    .optima-number-block {
      margin-top: 20px;
      //margin-bottom: 25px;
    }
  }

  .line {
    border: 0.5px solid #B0C7DD;
    background-color: #B0C7DD;
    margin: 4% 2%;
  }

  .right-part {
    width: 50%;
    padding: 2%;

    .title {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 15px;

      img {
        margin-right: 15px;
      }
    }

    .text {
      width: 90%;
      margin-top: 15px;
      margin-bottom: 10px;
    }

    .details-autoricarica {
      display: flex;
      flex-direction: column;
      padding-left: 0;
    }

    .attiva-button {
      margin-top: 20px;
      width: 170px;
    }

    .disable {
      background-color: gray;
      border: gray;
    }

    .payment-method {
      display: flex;
      align-items: center;

      .payment-icon {
        width: 50px;
        margin-right: 10px;
      }

      .text-position {
        margin-right: 25px;
      }

      .black-text {
        color: black !important;
      }

      .action-icons {
        width: 45px;
        cursor: pointer;
      }
    }
  }
}

button {
  border-radius: 10px;
  border: 1px solid #36749A;
  padding: 5px 15px;
  color: white;
  display: inline-block;
  margin-top: 2%;
  background: #36749A;
  font-size: 12px;
}

.form-control {
  border: 1px solid $menu-border;
  border-radius: 5px;
  font-weight: bold;
  color: $dark-blue;
}

.form-control-right-part {
  font-weight: bold;
  color: $dark-blue;
  border: 1px none;
  border-bottom-style: solid;
  padding-left: 1px;
  padding-bottom: 3px;
  margin-top: 25px;
  background-color: #f0f5f9;;
}

.button {
  text-align: center;
  border: 1px solid $dark-blue;
  color: $dark-blue;
  padding: 5px 15px;
  border-radius: 10px;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
}

.payment-button {
  background-color: #9AC641;
  color: white;
  border: #97B825;
  margin-top: 30px;
  display: block;
}

:-ms-input-placeholder {
  color: $menu-border;
}

::-ms-input-placeholder {
  color: $menu-border;
}

::placeholder {
  color: $menu-border;
  opacity: 1;
}

.scegli-numero {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

select {
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 85%;
  margin: auto;
  font-size: 20px;
}

.margin {
  margin-bottom: 20px;
  margin-top: 10px;
}

.header-modal-text {
  font-size: 30px
}

.body-modal-text {
  font-size: 20px
}

.margin-modal {
  margin-top: 40px;
}

.margin-modal-text {
  margin-top: 20px;
}

.position-text-modal {
  text-align: left;
  padding-left: 30px;
  padding-right: 30px;
}

.line-modal-window {
  border: 0.5px solid #B4CBE1;
  width: 75%;
  margin-top: 40px;
  margin-bottom: 20px;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.95);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  // min-height: 395px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 560px;
}

.flex-modal-inside {
  display: flex;
  flex-direction: column;
  align-items: center;

  .auto-ricarica-img {
    width: 64px;
    margin-top: 15px;
    margin-bottom: 35px;
  }

  .link {
    font-size: 20px;
    color: #00A9EA;
    text-decoration: underline;
    margin-top: 3%;
    margin-bottom: 3%;
  }

  .modal-window-button {
    font-size: 15px;
    width: 100px;
    height: 40px;
  }

  .modal-success-window-button {
    width: 205px;
    height: 40px;
    margin-top: 40px;
  }
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

@media screen and(min-width: 992px) and(max-width: 1280px) {
  .flex-panel {
    .left-part {
      .importo-size-img {
        width: 65px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .flex-panel {
    flex-direction: column;

    .left-part {
      width: 80%;

      .importo-size-img {
        width: 70px;
      }
    }

    .right-part {
      width: 80%;
    }
  }
}

@media screen and (max-width: 600px) {
  .inner-modal-div {
    width: 95%;
  }
}
