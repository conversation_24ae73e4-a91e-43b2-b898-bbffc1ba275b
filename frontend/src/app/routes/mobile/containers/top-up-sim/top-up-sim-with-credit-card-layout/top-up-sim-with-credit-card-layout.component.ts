import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ContractRecord} from '../../../../../common/model/mobile/contract-record/ContractRecord';
import {FormUtils} from '../../../../../common/utils/FormUtils';
import AdditionalProduct from '../../../../../common/model/mobile/contract-record/AdditionalProduct';
import 'rxjs/add/observable/empty';
import Validator from '../../../../../common/utils/Validator';
import {ActivatedRoute, Router} from '@angular/router';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import {MobileService} from '../../../../../common/services/mobile/mobile.service';
import {DialogModalActions} from '../../../../../redux/dialogModal/actions';
import {UserData} from '../../../../../common/model/userData.model';
import {Subscription} from 'rxjs/Subscription';
import {PaymentService} from '../../../../../common/services/payment/payment.service';
import {ObservableUtils} from '../../../../../common/utils/ObservableUtils';
import * as moment from 'moment';
import SubscriptionAutoricaricaRequest, {AutoricaricaInformationResponse} from '../../../models/PaymentAutoricaricaModels';
import {UserDataService} from '../../../../../common/services/user-data/userData.service';
import {PayPalService} from '../../../../../common/services/paypal/pay-pal-service.service';

@Component({
  selector: 'app-charging-sim-layout',
  templateUrl: './top-up-sim-with-credit-card-layout.component.html',
  styleUrls: ['./top-up-sim-with-credit-card-layout.component.scss']
})
export class TopUpSimWithCreditCardLayoutComponent implements OnInit, OnDestroy {

  @select(['mobile', 'contractRecords'])
  contractRecordsSelect: Observable<Array<ContractRecord>>;
  contractRecords: Array<ContractRecord> = [];
  userData: UserData;
  formGroup: FormGroup;
  mobileStateSubscription: Subscription;
  productActivationRecord: AdditionalProduct;
  showAutoTopUpModalWindow: boolean;
  showSuccessModalWindow: boolean;
  showSuccessRicaricaModalWindow: boolean;
  showErrorRicaricaModalWindow: boolean;
  showErrorModalWindow: boolean;
  showAutoRechargeEnabledModalWindow: boolean;
  showDetailsAboutAutoricarica: boolean;
  showDisableButton: boolean;
  importoAutoricarica: number;
  nextRenewalOffer: moment.Moment;
  newNextRenewalOffer: moment.Moment;
  nextAutoRecharge: moment.Moment;
  autoRicaricaInformation: AutoricaricaInformationResponse;
  pathParam: any;
  euroValues = {
    '5': true,
    '10': false,
    '15': false,
    '20': false,
    '50': false
  };
  euroImages = {
    '5': 'assets/img/importo-value/5_on.png',
    '10': 'assets/img/importo-value/10_off.png',
    '15': 'assets/img/importo-value/15_off.png',
    '20': 'assets/img/importo-value/20_off.png',
    '50': 'assets/img/importo-value/50_off.png'
  };

  constructor(protected fb: FormBuilder, protected route: ActivatedRoute,
              protected mobileService: MobileService, protected dialogModalActions: DialogModalActions,
              public router: Router, private paymentService: PaymentService, private userService: UserDataService,
              private payPalService: PayPalService) {
    this.mobileStateSubscription = this.contractRecordsSelect.subscribe(result => {
      this.userService.getUserData().subscribe(item => {
          this.userData = item;
          this.getInformationAboutRicarica(item.id.toString());
        }
      );
      this.contractRecords = result;
    });
    this.formGroup = this.buildForm();
    this.route.params.subscribe(param => {
      if (param.id) {
        this.pathParam = param['id'];
      }
    });
  }

  getInformationAboutRicarica(idClient: string) {
    this.paymentService.postPaymentAutoricaricaInformation(this.fillCheckAutoricaricaRequest(idClient)).subscribe(response => {
      this.importoAutoricarica = response.importo;
      if (response.RicaricaRicorrente === 1 && response.MaskedPan === null) {
        this.showDetailsAboutAutoricarica = false;
        this.showDisableButton = true;
      } else if (response.MaskedPan === null || response.RicaricaRicorrente === 0 || response.RicaricaRicorrente === null) {
        this.showDetailsAboutAutoricarica = false;
        this.route.queryParams.subscribe(params => {
          this.showDisableButton = params['success'] && params['success'] === 'true';
        });
      } else {
        this.autoRicaricaInformation = response;
        this.getInformationAboutTariff();
        this.showDetailsAboutAutoricarica = true;
      }
    });
  }

  fillCheckAutoricaricaRequest(idClient: string) {
    const request = new SubscriptionAutoricaricaRequest();
    request.COD_CLIENTE = idClient;
    for (let i = 0; i < this.contractRecords.length; i++) {
      if (this.contractRecords[i].msisdnId === parseInt(this.formGroup.controls.msisdnId.value, 10)) {
        request.SUBSCRIPTION_ID = this.contractRecords[i].id.toString();
        this.formGroup.get('msisdnId').patchValue(this.contractRecords[i].msisdnId);
        break;
      }
    }
    return request;
  }

  buildForm() {
    const formGroup = this.fb.group({
      msisdnId: [null, [Validators.required]],
      optimaNumber: [null, [Validators.required, Validator.withLength(6, 10)]],
      amount: [5, [Validators.required]]
    });
    this.setCommonFormGroupConfiguration(formGroup);
    return formGroup;
  }

  setCommonFormGroupConfiguration(formGroup: FormGroup) {
    formGroup.controls.msisdnId.valueChanges.subscribe((input: string) => {
      if (input) {
        formGroup.controls.optimaNumber.disable({emitEvent: false});
      } else {
        formGroup.controls.optimaNumber.enable({emitEvent: false});
      }
      this.productActivationRecord = null;
    });

    formGroup.controls.optimaNumber.valueChanges.subscribe((input: string) => {
      this.productActivationRecord = null;
      if (input) {
        formGroup.controls.msisdnId.disable({emitEvent: false});
      } else {
        formGroup.controls.msisdnId.enable({emitEvent: false});
      }
    });

    this.route.params.subscribe(param => {
      if (param.id) {
        formGroup.controls.msisdnId.setValue(parseInt(param.id, 10));
      }
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
    this.route.queryParams.subscribe(params => {
      const {esito, operazione} = params;
      if (operazione === 'autoricarica') {
        this.showSuccessModalWindow = esito === 'OK';
        this.showErrorModalWindow = esito === 'KO' || esito === 'ANNULLO';
      } else if (operazione === 'ricarica') {
        this.showSuccessRicaricaModalWindow = esito === 'OK';
        this.showErrorRicaricaModalWindow = esito === 'KO' || esito === 'ANNULLO';
      }
    });
  }

  replenishAccount() {
    if (this.formGroup.valid && !this.isUserChooseValue()) {
      const number = this.formGroup.value.msisdnId ? this.formGroup.value.msisdnId : '39' + this.formGroup.value.optimaNumber;
      this.payPalService.isOptimaNumber(number).subscribe(resp => {
        if (resp === true) {
          this.formGroup.controls['optimaNumber'].setErrors({'numberExistence': true});
          return;
        }
        this.router.navigate([this.router.url.replace(this.pathParam, number) + '/' + this.formGroup.controls['amount'].value + '/metodi']);
      });
    } else {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    }
  }

  isUserChooseValue() {
    return !Object.values(this.euroValues).includes(true);
  }

  changeStyle(euroAmount) {
    const isSelected = this.euroValues[euroAmount];
    this.resetEuroValues();
    this.euroValues[euroAmount] = !isSelected;
    this.updateEuroImages();
    if (!isSelected) {
      this.formGroup.get('amount').patchValue(euroAmount);
    }
    this.isUserChooseValue();
  }

  resetEuroValues() {
    Object.keys(this.euroValues).forEach(key => {
      this.euroValues[key] = false;
    });
  }

  updateEuroImages() {
    Object.keys(this.euroImages).forEach(key => {
      this.euroImages[key] = this.euroValues[key]
        ? `assets/img/importo-value/${key}_on.png`
        : `assets/img/importo-value/${key}_off.png`;
    });
  }

  getInformationAboutTariff() {
    for (let i = 0; i < this.contractRecords.length; i++) {
      if (this.contractRecords[i].msisdnId === this.formGroup.controls.msisdnId.value) {
        const unit = this.contractRecords[i].additionalProducts[0].product.renewalPeriodUnit.name.toLowerCase();
        this.nextRenewalOffer = moment(this.contractRecords[i].additionalProducts[0].purchasedOn).add(this.contractRecords[i].additionalProducts[0].product.renewalPeriod, unit === 'day' ? 'd' : 'M');
        // console.log('nextRenewalOffer->'+this.nextRenewalOffer)
        this.newNextRenewalOffer = moment(this.contractRecords[i].additionalProducts[0].expiresOn);
        // console.log('newNextRenewalOffer->'+this.newNextRenewalOffer)
        if (moment() > moment(this.newNextRenewalOffer).subtract(2, 'days')) {
          this.nextAutoRecharge = moment(this.newNextRenewalOffer).add(this.contractRecords[i].additionalProducts[0].product.renewalPeriod, unit === 'day' ? 'd' : 'M').subtract(2, 'days');
        } else {
          this.nextAutoRecharge = moment(this.newNextRenewalOffer).subtract(2, 'days');
        }
      }
    }
  }

  openAutoTopUpModalWindow() {
    this.getInformationAboutTariff();
    this.showAutoTopUpModalWindow = true;
  }

  openAutoRechargeEnabledModalWindow() {
    this.getInformationAboutTariff();
    this.showAutoRechargeEnabledModalWindow = true;
  }

  hideModalWindows() {
    this.showAutoTopUpModalWindow = false;
    this.showSuccessModalWindow = false;
    this.showSuccessRicaricaModalWindow = false;
    this.showErrorRicaricaModalWindow = false;
    this.showAutoRechargeEnabledModalWindow = false;
    this.showErrorModalWindow = false;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.mobileStateSubscription]);
  }
}
