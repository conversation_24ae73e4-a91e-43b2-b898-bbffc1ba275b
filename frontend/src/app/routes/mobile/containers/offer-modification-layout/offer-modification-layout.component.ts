import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON>er, FormGroup, Validators} from '@angular/forms';
import {ContractRecord} from '../../../../common/model/mobile/contract-record/ContractRecord';
import {MobileService} from '../../../../common/services/mobile/mobile.service';
import {NotificationService} from '../../../../common/services/notification/notification.service';
import {ActivateOptionResponse} from '../../../../common/model/mobile/ActivateOptionResponse';
import ConfirmModalOption from '../../../../common/model/mobile/ConfirmModalOption';
import {ActivatedRoute} from '@angular/router';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {ProductType} from '../../../../common/model/mobile/ProductType';
import {Product} from '../../../../common/model/mobile/product-record/Product';
import {messages} from '../../config/config';

@Component({
  selector: 'app-offer-modification-layout',
  templateUrl: './offer-modification-layout.component.html',
  styleUrls: ['./offer-modification-layout.component.scss']
})
export class OfferModificationLayoutComponent implements OnInit {

  formGroup: FormGroup;

  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;

  productRecords: Array<Product>;

  selectedRecord: ContractRecord;

  confirmModalOption: ConfirmModalOption;

  infoModalOption: ConfirmModalOption;
  activationCost: number;
  productName: string;

  constructor(private mobileService: MobileService, private fb: FormBuilder, private notificationService: NotificationService, private route: ActivatedRoute) {
    this.formGroup = this.fb.group({
      msisdnId: [null, [Validators.required]]
    });

    this.formGroup.controls.msisdnId.valueChanges.flatMap((input: string) => {
      if (input && this.contractRecords) {
        const msisdnId = parseInt(input, 10);
        return this.contractRecords.map((items) => items.find(item => item.msisdnId === msisdnId));
      }
      return Observable.empty();
    }).subscribe((selectedRecord: ContractRecord) => {
      if (selectedRecord) {
        this.selectedRecord = selectedRecord;
        this.loadProductRecords(selectedRecord, ProductType.PARRENT);
      }
    });
    this.route.params.subscribe(param => {
      if (param.id) {
        this.formGroup.controls.msisdnId.setValue(parseInt(param.id, 10));
      }
    });
  }

  loadProductRecords(contractRecord: ContractRecord, productTypeId: number) {
    this.mobileService.loadProductRecordsOffersByProductType(contractRecord, productTypeId).subscribe((response) => {
      this.productRecords = response;
    });
  }

  changeTariffPlan(newOptionId) {
    if (newOptionId && this.selectedRecord) {
      this.mobileService.changeTariffPlan(newOptionId, this.selectedRecord).subscribe(
        (response: ActivateOptionResponse) => {
          if (response.code === 0) {
            this.loadProductRecords(this.selectedRecord, ProductType.PARRENT);
            this.openInfoModal();
          } else {
            this.notificationService.showInfoMessage(response.errorMessage);
          }
        },
        () => {
          this.notificationService.showInfoMessage(messages.changeTariffPlanFailedMessage);
        }
      );
    }
  }

  openModal(record: Product) {
    if (record) {
      this.activationCost = parseFloat(record.activationCost);
      this.productName = record.productName;
      const modalOption = new ConfirmModalOption();
      // modalOption.serviceName = record.productName;
      modalOption.onSubmit = () => this.changeTariffPlan(record.productId);
      this.confirmModalOption = modalOption;
    }
  }

  openInfoModal() {
    const modalOption = new ConfirmModalOption();
    modalOption.showConfirmButtons = false;
    this.infoModalOption = modalOption;
  }

  ngOnInit() {
  }

}
