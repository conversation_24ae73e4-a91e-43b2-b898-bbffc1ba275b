import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {OffersInAppService} from '../../../offers-in-apps/service/offers-in-app.service';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {AddOn, Offer5GResponse, Sim} from '../../../offers-in-apps/models/Offer5GModels';

enum ViewLayoutMode {
  ACTIVATION_WITH_DELAY = 'activationWithDelay',
  ACTIVATION_24_HOURS = 'activation24Hours',
  ACTIVE_SERVICE = 'activeService'
}

enum ViewModalMode {
  CONTRACT_INFO = 'contractInfo',
  ACTIVATION_CONFIRMATION = 'activationConfirmation',
  ACTIVATION_ALREADY_IN_PROGRESS = 'activationAlreadyInProgress',
  DEACTIVATION_SURVEY = 'deactivationSurvey',
  DEACTIVATION_CONFIRMATION = 'deactivationConfirmation',
  DEACTIVATION_SUCCESS = 'deactivationSuccess'
}

@Component({
  selector: 'app-information-5g-layout',
  templateUrl: './information-5g-layout.component.html',
  styleUrls: ['./information-5g-layout.component.scss']
})
export class Information5gLayoutComponent implements OnInit {

  viewMode: ViewLayoutMode;
  ViewModeLayout = ViewLayoutMode;
  viewModalMode: ViewModalMode;
  ViewModeModal = ViewModalMode;
  selectedSim: Sim;
  allSims: Sim[] = [];
  showGeneralErrorWindow: boolean;
  targetMsisdnId: string;
  selectedReason: string = '';
  deactivationReasons = [
    {value: 'expensive', label: 'Troppo costoso'},
    {value: 'connectivity', label: 'Problemi di connettività'},
    {value: 'other', label: 'Altro'}
  ];

  constructor(private readonly activatedRoute: ActivatedRoute,
              private readonly offerService: OffersInAppService,
              private readonly incidentEventService: IncidentEventService) {
  }

  ngOnInit() {
    this.targetMsisdnId = this.activatedRoute.snapshot.params['id'];
    this.loadOffer5GData(localStorage.getItem('clientId'), null);
  }

  loadOffer5GData(clientId: string, subscriptionId: string) {
    this.offerService.getOffer5GData(clientId, subscriptionId).subscribe((data: Offer5GResponse) => {
      this.allSims = data.sim.filter(sim => (sim.addOnActive && sim.addOnActive.length > 0) ||
        (sim.addOnAviable && sim.addOnAviable.length > 0));
      const targetSim = this.allSims.find(sim => sim.msisdnId === this.targetMsisdnId);
      if (targetSim) {
        this.selectSim(targetSim);
      } else if (this.allSims.length > 0) {
        this.selectSim(this.allSims[0]);
      }
    }, () => {
      this.showGeneralErrorWindow = true;
    });
  }

  selectSim(sim: Sim) {
    this.selectedSim = sim;
    const activeAddOn = sim.addOnActive && sim.addOnActive.find(addon => addon.codice === '5G');
    if (activeAddOn) {
      this.viewMode = ViewLayoutMode.ACTIVE_SERVICE;
      return;
    }
    const currentAddOn = sim.addOnAviable && sim.addOnAviable.find(addon => addon.codice === '5G');
    if (currentAddOn) {
      if (currentAddOn.numRichiesteAttivazioni === 0) {
        this.viewMode = ViewLayoutMode.ACTIVATION_24_HOURS;
      } else {
        this.viewMode = ViewLayoutMode.ACTIVATION_WITH_DELAY;
      }
    }
  }

  onSimChange(event: any) {
    const selectedMsisdnId = event.target.value;
    const sim = this.allSims.find(s => s.msisdnId === selectedMsisdnId);
    if (sim) {
      this.selectSim(sim);
    }
  }

  get current5GAddOn(): AddOn | undefined {
    return this.selectedSim && this.selectedSim.addOnAviable && this.selectedSim.addOnAviable.find(addon =>
      addon.codice === '5G');
  }

  get active5GAddOn(): AddOn | undefined {
    return this.selectedSim && this.selectedSim.addOnActive && this.selectedSim.addOnActive.find(addon =>
      addon.codice === '5G');
  }

  openModalPreActivation(): void {
    if (this.current5GAddOn && this.current5GAddOn.richiestaInCorso) {
      this.viewModalMode = ViewModalMode.ACTIVATION_ALREADY_IN_PROGRESS;
      return;
    }
    this.viewModalMode = ViewModalMode.CONTRACT_INFO;
  }

  closeModal(): void {
    this.viewModalMode = null;
  }

  activateNow(): void {
    this.closeModal();
    if (this.current5GAddOn) {
      this.incidentEventService.openIncidentEventoForActivate5GOffer(
        this.selectedSim.subscriptionId,
        this.current5GAddOn.codiceOfferta,
        this.current5GAddOn.canoneMese,
        200007
      ).subscribe(response => {
        if (response.status === 'OK') {
          this.viewModalMode = ViewModalMode.ACTIVATION_CONFIRMATION;
        } else {
          this.showGeneralErrorWindow = true;
        }
      });
    }
  }

  startDeactivation(): void {
    this.viewModalMode = ViewModalMode.DEACTIVATION_SURVEY;
  }

  selectReason(reason: string): void {
    this.selectedReason = reason;
  }

  proceedToConfirmation(): void {
    if (this.selectedReason) {
      this.viewModalMode = ViewModalMode.DEACTIVATION_CONFIRMATION;
    }
  }

  confirmDeactivation(): void {
    this.viewModalMode = null;
    if (this.active5GAddOn) {
      this.incidentEventService.openIncidentForDeactivate5G(
        this.selectedSim.subscriptionId,
        this.active5GAddOn.codiceOfferta,
        this.active5GAddOn.canoneMese,
        this.selectedReason,
        200007
      ).subscribe(response => {
        if (response.status === 'OK') {
          this.viewModalMode = ViewModalMode.DEACTIVATION_SUCCESS;
        } else {
          this.showGeneralErrorWindow = true;
        }
      });
    }
  }

  cancelDeactivation(): void {
    if (this.viewModalMode === ViewModalMode.DEACTIVATION_SURVEY) {
      this.closeModal();
    } else if (this.viewModalMode === ViewModalMode.DEACTIVATION_CONFIRMATION) {
      this.viewModalMode = ViewModalMode.DEACTIVATION_SURVEY;
    }
  }

  get isProceedEnabled(): boolean {
    return this.selectedReason !== '';
  }

  reloadPage() {
    window.location.reload();
  }
}
