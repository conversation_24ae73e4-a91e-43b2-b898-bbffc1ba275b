import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AddOptionsRichiediPortabilitaComponent } from './add-options-richiedi-portabilita.component';

describe('AddOptionsRichiediPortabilitaComponent', () => {
  let component: AddOptionsRichiediPortabilitaComponent;
  let fixture: ComponentFixture<AddOptionsRichiediPortabilitaComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AddOptionsRichiediPortabilitaComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddOptionsRichiediPortabilitaComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
