import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MobileService } from '../../../../common/services/mobile/mobile.service';
import { ContractRecord } from '../../../../common/model/mobile/contract-record/ContractRecord';
import { ProductRecord } from '../../../../common/model/mobile/product-record/ProductRecord';
import { ActivateOptionResponse } from '../../../../common/model/mobile/ActivateOptionResponse';
import { NotificationService } from '../../../../common/services/notification/notification.service';
import ConfirmModalOption from '../../../../common/model/mobile/ConfirmModalOption';
import { ActivatedRoute } from '@angular/router';
import { select } from '@angular-redux/store';
import { Observable } from 'rxjs/Observable';
import { ProductType } from '../../../../common/model/mobile/ProductType';

@Component({
  selector: 'app-add-options-layout',
  templateUrl: './add-options-layout.component.html',
  styleUrls: ['./add-options-layout.component.scss']
})
export class AddOptionsLayoutComponent implements OnInit {

  formGroup: FormGroup;

  @select(['mobile', 'contractRecords'])
  contractRecords: Observable<Array<ContractRecord>>;

  productRecords: Array<ProductRecord>;

  selectedRecord: ContractRecord;

  statusMessage: string;

  confirmModalOption: ConfirmModalOption;
  errorModalOption: ConfirmModalOption;
  infoModalOption: ConfirmModalOption;
  balance = 0;

  constructor(private mobileService: MobileService, private fb: FormBuilder, private notificationService: NotificationService, private route: ActivatedRoute) {
    this.formGroup = this.fb.group({
      msisdnId: [null, [Validators.required]]
    });
    this.formGroup.controls.msisdnId.valueChanges.flatMap((input: string) => {
      if (input && this.contractRecords) {
        const msisdnId = parseInt(input, 10);
        localStorage.setItem('msisdnId', msisdnId.toString());
        return this.contractRecords.map((items) => items.find(item => item.msisdnId === msisdnId));
      }
      return Observable.empty();
    }).subscribe((selectedRecord: ContractRecord) => {
      if (selectedRecord) {
        this.selectedRecord = selectedRecord;
        this.mobileService.loadSimBalance(selectedRecord.msisdnId).subscribe(information =>
          this.balance = information ? Number(information) : 0);
        this.loadProductRecords(selectedRecord, ProductType.CHILD);
      }
    });
    this.route.params.subscribe(param => {
      if (param.id) {
        this.formGroup.controls.msisdnId.setValue(parseInt(param.id, 10));
      }
    });
  }

  ngOnInit() {
  }

  loadProductRecords(contractRecord: ContractRecord, productTypeId: number) {
    this.mobileService.loadProductRecordsByProductType(contractRecord, productTypeId).subscribe((response) => {
      this.statusMessage = response && response.length > 0 ? '' : 'Le comunichiamo che al momento non ci sono opzioni attivabili sul suo numero.';
      this.productRecords = response;
    });
  }

  activateOption(newOption: ProductRecord) {
    if (this.balance >= newOption.activationPrice) {
      if (newOption.id && this.selectedRecord) {
        this.mobileService.activateNewOption(newOption.id, this.selectedRecord).subscribe((response: ActivateOptionResponse) => {
          if (response.code === 0) {
            this.loadProductRecords(this.selectedRecord, ProductType.CHILD);
            this.openInfoModal();
          } else {
            this.notificationService.showInfoMessage(response.errorMessage);
          }
        });
      }
    } else {
      const modalOption = new ConfirmModalOption();
      modalOption.serviceName = newOption.name;
      modalOption.showConfirmButtons = false;
      this.errorModalOption = modalOption;
    }
  }

  openModal(record: ProductRecord) {
    if (record) {
      const modalOption = new ConfirmModalOption();
      modalOption.serviceName = record.name;

      modalOption.onSubmit = () => this.activateOption(record);
      this.confirmModalOption = modalOption;
    }
  }

  openInfoModal() {
    const modalOption = new ConfirmModalOption();
    modalOption.showConfirmButtons = false;
    this.infoModalOption = modalOption;
  }
}
