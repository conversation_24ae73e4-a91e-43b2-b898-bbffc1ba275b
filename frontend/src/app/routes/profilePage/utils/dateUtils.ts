export function formatDateWithSlashesToJsDate(targetDate: string): Date {
  const arrDate: string[] = targetDate.split('/');
  return new Date(Number(arrDate[2]), Number(arrDate[1]) - 1, Number(arrDate[0]));
}

export function formatDateToStringMonthAndYearWithSlashes(targetDate: Date): string {
  const month = targetDate.getMonth().valueOf() + 1;
  const year = targetDate.getFullYear();
  return month > 9 ? `${month}/${year}` : `0${month}/${year}`;
}

export function formatDateToStringDayAndMonthAndYearWithSlashes(targetDate: Date): string {
  const year = targetDate.getFullYear();
  const month = targetDate.getMonth().valueOf() + 1;
  const day = targetDate.getDate().valueOf();
  return month || day > 9 ? `${day}/${month}/${year}` : `0${day}/0${month}/${year}`;
}
