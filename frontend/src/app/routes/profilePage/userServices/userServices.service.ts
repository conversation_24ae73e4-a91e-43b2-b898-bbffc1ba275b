import {Injectable} from '@angular/core';
import {Observable} from 'rxjs/Observable';

import 'rxjs/add/operator/delay';
import 'rxjs/add/operator/do';
import { UserServices } from '../../../common/model/services/userServices.model';
import { HttpClient } from '@angular/common/http';
import { InActiveServiceStatus } from '../../../common/enum/ServiceStatus';

@Injectable()
export class UserServicesService {

  constructor(private http: HttpClient) {
  }

  public getUserData(): Observable<UserServices[]> {
    return this.http.get<UserServices[]>(`api/services/${localStorage.getItem('clientId')}`);
  }

  public getActiveServices(data) {
    const result = {};
    if (data) {
      data.forEach((value) => {
        const activeUtilities = value.utilities.filter((utility) => utility.status &&
          (utility.status.toUpperCase() === 'ATTIVATO' ||
          utility.status.toUpperCase() === 'ATTIVO' ||
          utility.status.toUpperCase() === 'CANCELLATA' ||
            utility.status.toUpperCase() === 'IN_ATTIVAZIONE' ||
            utility.status.toUpperCase() === 'ACTIVE' ||
            utility.status.toUpperCase() === 'INITIACTIVE' ||
            utility.status === 'Hard suspension' ||
            utility.status === 'Soft suspension' ||
            utility.status === 'Lost sim'));
        if (activeUtilities.length > 0) {
          result[value.serviceName] = {'serviceName': value.serviceName, utilities: activeUtilities};
        }
      });
    }
    return result;
  }

  public getInActiveServices(data: Array<UserServices>) {
    const result = {};
    data.forEach(value => {
      const activeUtilities = value.utilities.filter(utility => InActiveServiceStatus[utility.status]);
      if (activeUtilities.length > 0) {
        result[value.serviceName] = {'serviceName': value.serviceName, utilities: activeUtilities};
      }
    });
    return result;
  }

  public isActiveServices(data) {
    const result = {};
    if (data) {
        const activeUtilities = data.utilities.filter((value) => value.status &&
          (value.status === 'ATTIVATO' ||
          value.status === 'CANCELLATA' ||
            value.status.toUpperCase() === 'ACTIVE' ||
            value.status.toUpperCase() === 'IN_ATTIVAZIONE' ||
          value.status.toUpperCase() === 'INITIACTIVE'));
      if (activeUtilities.length <= 0) {
      } else {
        result[data.serviceName] = {'serviceName': data.serviceName, utilities: activeUtilities};
      }
    }
    return result;
  }

  getCardStatus(service) {
    let tempStatus = 'DISATTIVATO';
    service.utilities.forEach(utilite => {
      if (utilite.status) {
        if (utilite.status.toLowerCase() === 'attivato') {
          tempStatus = 'ATTIVATO';
          return tempStatus;
        }
        else if (utilite.status.toLowerCase() === 'in_attivazione') {
          tempStatus = 'in_attivazione';
          return tempStatus;
        }
    }

    });
    return tempStatus;
  }
}
