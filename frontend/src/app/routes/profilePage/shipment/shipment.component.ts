import {Component, OnInit} from '@angular/core';
import {Shipment} from './shipment.model';
import {ShipmentService} from './shipment.service';


@Component({
  selector: 'app-user-shipment',
  templateUrl: './shipment.component.html',
  styleUrls: ['./shipment.component.scss']
})
export class ShipmentComponent implements OnInit {


  shipments: Array<Shipment>;
  client = {
    name: localStorage.getItem('clientName'),
    sName: localStorage.getItem('clientSName')
  };

  columns: Array<String> = [
    'Data creazione', 'Cliente', 'CF/PI', 'Indirizzo di spedizione', 'Cap', 'Comune', 'Tipo articolo',
    'Matricola', 'Quantita', 'Stato spedizione', 'Data modifica stato', 'Causale', 'Tracking number'
  ];

  constructor(private shipmentService: ShipmentService) {
    this.shipmentService.getInvoiceData().subscribe(shipments => {
      this.shipments = shipments.sort((a, b) => +b.startDate - +a.startDate);
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
  }
}
