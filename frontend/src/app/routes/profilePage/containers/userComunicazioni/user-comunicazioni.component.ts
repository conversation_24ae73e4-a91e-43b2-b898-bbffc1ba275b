import {AfterViewChecked, ChangeDetector<PERSON>ef, Component, <PERSON><PERSON><PERSON><PERSON>} from '@angular/core';
import 'rxjs/add/observable/forkJoin';
import {Observable} from 'rxjs/Observable';
import * as moment from 'moment';

import {Allegati} from '../../../../common/model/communications/ComunicazioniNote';
import {BehaviorSubject} from 'rxjs/BehaviorSubject';
import CommunicationState from '../../../../redux/model/CommunicationState';
import {CommunicationActions} from '../../../../redux/communication/actions';
import {Subscription} from 'rxjs/Subscription';
import {select} from '@angular-redux/store';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';


@Component({
  selector: 'app-user-comunicazioni',
  templateUrl: './user-comunicazioni.component.html',
  styleUrls: ['./user-comunicazioni.component.scss']
})
export class UserComunicazioniComponent implements OnD<PERSON>roy, AfterViewChecked  {

  communicationData: Array<any>;

  paginator: BehaviorSubject<Array<any>>;

  communicationTableData: Array<any>;

  @select(['communication'])
  communication: Observable<CommunicationState>;

  communicationSubscription: Subscription;

  constructor(private cdRef: ChangeDetectorRef, private communicationActions: CommunicationActions) {
    this.communicationActions.loadCommunicationDataIfNotExist(localStorage.getItem('clientId'));
    this.communicationSubscription = this.communication.subscribe((communicationState) => {
      const {communicationEmailInfo, recommendedBlocks, communicationNote} = communicationState;
      if (recommendedBlocks && communicationEmailInfo && communicationNote) {
        const data = recommendedBlocks.map((value) => {
          return {
            creationDate: value.recommendedDate,
            subject: 'Raccomandata',
            fileUrl: this.getPdfFileUrl(value.pdfName)
          };
        }).concat(communicationEmailInfo.map((value) => {
          return {
            creationDate: value.creationDate,
            subject: value.subject,
            fileUrl: null
          };
        })).concat(this.reorgaize(communicationNote)
        );
        this.communicationData = data.sort((o1, o2) => o2.creationDate - o1.creationDate);
        this.communicationTableData = this.communicationData;
        this.paginator = new BehaviorSubject([]);
      }
    });
  }

  getPdfFileUrl(fileName: string) {
    return fileName && `/api/communication/recommended/pdf/${localStorage.getItem('clientId')}?file=${fileName}&access_token=${localStorage.getItem('access_token')}`;
  }

  getPdfFileUrlForCommunicationNote(allegati: Allegati) {
    return allegati.uri && `/api/communication/note/pdf/${localStorage.getItem('clientId')}?file=${allegati.uri}&access_token=${localStorage.getItem('access_token')}`;
  }

  applyFilter(filteringDate: string) {
    if (!filteringDate) {
      this.communicationTableData = this.communicationData;
    } else {
      this.communicationTableData = this.communicationData.filter((item): boolean =>
        moment(item.creationDate).format('YYYY-MM-DD').toString() === filteringDate);
    }
  }

  reorgaize(data) {
    const mass = [];
    data.filter((value) => value.allegati.filter(allegati => allegati.uri).length).forEach(value => {
      mass.push(
        value.allegati.map(allegati => {
          if (allegati.uri) {
            return {
              creationDate: value.dataNota,
              subject: 'Comunicazione Optima',
              fileUrl: this.getPdfFileUrlForCommunicationNote(allegati)
            };
          }
        })
      );
    });
    return [].concat.apply([], mass);
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.communicationSubscription]);
  }
  ngAfterViewChecked() {
    this.cdRef.detectChanges();
  }
}
