<div class="col-md-9 app--user-data" *ngIf="!userData">
  <div class="user-data-table-block" style="padding: 15px 10px">
    <span>Non risulta alcuna utenza attiva</span>
  </div>
</div>
<div class="col-md-9 app--user-data" *ngIf="userData" [formGroup]="formGroup">
  <div class="row">
    <div class="col-md-12 user-data-block">
      <div class="row page-title">
        <div class="title-image"></div>
        <div class="text">I TUO DATI</div>
      </div>
      <div class="row client-name">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 font-weight-bold">Cliente</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6">
            <app-user-data-editor [formGroup]="formGroup" name="cliente"
                                  [modifiable]="isBusiness" [emptyOnEdit]="true"
                                  [onAccept]="changePersonalData(incidentEventCategory
                                  .CHANGE_SOCIAL_NAME)"
                                  placeholder="Inserisci la nuova ragione sociale">
              <confirm-message>
                <span
                  class="confirm-message">La nuova ragione sociale inserita è <b>
                  {{formGroup.controls['cliente'].value}}</b>,
                per completare l’operazione clicca su “Conferma”</span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>
      </div>
      <div class="row user-data-table-block">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row" *ngIf="userData.fiscalCode">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Codice Fiscale:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">{{userData.fiscalCode}}</div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row" *ngIf="userData.vatNumber">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">P.IVA</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">{{userData.vatNumber}}</div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Cod. Cliente:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">{{userData.id}}</div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Modalità di ricezione fattura:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-user-data-editor [formGroup]="formGroup" name="modalitaFattura" [modifiable]="true"
                                  [onAccept]="changeShipmentMethod(incidentEventCategory.CHANGE_INVOICE_SHIPPING_METHOD)"
                                  type="select" [options]="modalitaFatturaOptions" [readyToEdit]="readyToEdit">
              <confirm-message *ngIf="formGroup.controls['modalitaFattura'].value==='Elettronica'">
                <span class="confirm-message">Stai richiedendo la spedizione elettronica della fattura Optima. Riceverai la fattura al tuo
                  indirizzo mail <b>{{formGroup.controls['email'].value}}</b>. Per modificare la mail una volta completata questa
                  operazione clicca su “Variazione E-mail".</span>
              </confirm-message>
              <confirm-message *ngIf="formGroup.controls['modalitaFattura'].value==='Cartacea'">
                <span class="confirm-message">Stai richiedendo la spedizione cartacea della fattura Optima. Riceverai la fattura al tuo
                  indirizzo <b>{{formGroup.controls['officeAddress'].value}}</b>. Per modificare l’indirizzo di spedizione
                  della fattura una volta completata questa operazione clicca su “Variazione Indirizzo di fatturazione".
                </span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row"
             *ngIf="showAddress&& formGroup.controls['modalitaFattura'].value==='Cartacea'">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Indirizzo di fatturazione:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-address-editor [formGroup]="formGroup" name="indirizzoFatturazione"
                                [options]="indirizzoFatturazioneOptions"
                                [onAccept]="changeAddress(incidentEventCategory.CHANGE_BILLING_ADDRESS)"
                                placeholder="Inserisci il nuovo indirizzo di fatturazione" [emptyOnEdit]="true">
            </app-address-editor>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Indirizzo/Sede legale:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-address-editor [formGroup]="formGroup" name="officeAddress" checkboxName="officeAddress"
                                placeholder="Inserisci il nuovo sede legale" [emptyOnEdit]="true"
                                [onAccept]="changeAddress(incidentEventCategory.CHANGE_OFFICE_ADDRESS)">
            </app-address-editor>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12  detail-row" *ngIf="shouldShowModalitaPagamentoRow">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Modalità di pagamento:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <span>{{userData.paymentData.modalitaPagamento}}</span>
            <span class="fa fa-pencil" (click)="switchDisplay()"></span>
            <span class="payment-info" *ngIf="hasCreditCardInfo">
              <span class="{{creditCardBrand}}"></span>{{userData.paymentData.maskedPan}}
              - {{userData.paymentData.expireMonth}}/{{userData.paymentData.expireYear}}</span>
          </div>
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Numeri di riferimento:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
           <app-user-data-editor [formGroup]="formGroup" name="numeriRiferimento"
                                  [onAccept]="changePersonalData(incidentEventCategory.CHANGE_TELEPHONE_NUMBER)"
                                  [modifiable]="true" [emptyOnEdit]="true"
                                  placeholder="Inserisci il nuovo numero di riferimento">
              <confirm-message>
                <span
                  class="confirm-message">Il nuovo numero di riferimento inserito è <b>“{{formGroup.controls['numeriRiferimento'].value}}
                  ”</b>,
                  per completare l’operazione clicca su "Conferma".
                </span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">E-mail:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-user-data-editor [formGroup]="formGroup" name="email"
                                  [onAccept]="changePersonalData(incidentEventCategory.CHANGE_EMAIL)"
                                  [modifiable]="true" [emptyOnEdit]="true"
                                  placeholder="Inserisci la nuova email">
              <confirm-message>
                <span class="confirm-message">La nuova email inserita è <b>“{{formGroup.controls['email'].value}}”</b>,
                  per completare l’operazione clicca su "Conferma"
                </span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject subject-info">Codice di Sicurezza:
            <app-info name="passwordIdentification">
              <info-button><i class="info-circle">i</i></info-button>
              <info-message>Il Codice di Sicurezza è una chiave segreta da utilizzare per essere sicuri
                dell’autenticità delle tue interazioni con Optima.
              </info-message>
            </app-info>
          </div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-user-data-editor [formGroup]="formGroup" name="passwordIdentification"
                                  [onAccept]="changePasswordIdentification()"
                                  [modifiable]="true" [emptyOnEdit]="true"
                                  placeholder="Inserisci il nuovo Codice di Sicurezza">
              <confirm-message>
                <span class="confirm-message">Il nuovo codice di sicurezza inserito è
                  <b>“{{formGroup.controls['passwordIdentification'].value}}”</b>,
                  per completare l’operazione clicca su "Conferma"
                </span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Pec:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-user-data-editor [formGroup]="formGroup" name="pec"
                                  [onAccept]="changePersonalData(incidentEventCategory.PEC_VARIATION)"
                                  [modifiable]="true" [emptyOnEdit]="true"
                                  placeholder="Inserisci la nuova pec">
              <confirm-message>
                <span class="confirm-message">La nuova pec inserita è <b>“{{formGroup.controls['pec'].value}}”</b>,
                  per completare l’operazione clicca su "Conferma"
                </span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
          <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Codice Destinatario:</div>
          <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
            <app-user-data-editor [formGroup]="formGroup" name="recipientCode"
                                  [onAccept]="changePersonalData(incidentEventCategory.CHANGE_RECIPIENT_CODE)"
                                  [modifiable]="true" [emptyOnEdit]="true"
                                  placeholder="Inserisci il nuovo codice destinatario">
              <confirm-message>
                <span
                  class="confirm-message">Il nuovo codice destinatario inserito è <b>“{{formGroup.controls['recipientCode'].value}}
                  ”</b>,
                  per completare l’operazione clicca su "Conferma"
                </span>
              </confirm-message>
            </app-user-data-editor>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<app-modal #infoModal class="info-modal">
  <modal-description>
    <div *ngIf="isCreditCardEnabled | async" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 detail-row">
      <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Attiva “Addebito ricorrente su carta di Credito”</div>
      <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
        <a class="fa fa-pencil" tooltip="Attiva" routerLink="/profile/confirm-payment"></a>
      </div>
    </div>
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12  detail-row">
      <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 subject">Attiva “Domiciliazione Bancaria / Bonifico Bancario / Bollettino"</div>
      <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 data">
        <span class="fa fa-file-pdf-o" tooltip="Scarica Modulo" (click)="getDocument()"></span>
      </div>
    </div>
  </modal-description>
</app-modal>
