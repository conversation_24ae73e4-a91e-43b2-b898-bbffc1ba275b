@import "~app/shared/styles/colors";

.container-fluid {
  padding: 40px 0 0 0;
}

.panel.panel-default {
  width: 100%;
  margin: auto;

}

.row {
  margin: auto;
}

.page-title {
  border: 2px solid $menu-border;
  border-radius: 5px;
  height: 45px;

  .title-image {
    background: url("/assets/img/optima/Set_Icone_AreaClienti_ItuoiDati.png") no-repeat center;
    background-size: contain;
    width: 50px;
    height: 50px;
    float: left;
  }

  .text {
    color: $dark-blue;
    margin-top: 11px;
    float: left;
  }
}

.client-name {
  font-size: 16px;
  margin-top: 2%;
  border: 1px solid $menu-border;
  border-radius: 5px;
  min-height: 50px;
  color: $dark-blue;
  background-color: #ffffff;

  div {
    margin-top: 6px;
  }
}

.user-data-table-block {
  margin-top: 2%;
  color: $dark-blue;
  border: 1px solid $menu-border;
  border-radius: 5px;

  .material {
    border-radius: 5px;
  }

  .detail-row {
    display: inline-flex;
    padding-right: 0;
    padding-left: 0;

    div {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    .data {
      font-size: 14px;
      font-weight: 400;
      .payment-info{
        display: block;
      }
    }

    .subject {
      font-weight: bold;
      border-right: 2px solid $menu-border;
      padding-right: 0;
    }

    .subject-info {
      display: flex;

      .info-circle {
        width: 19px;
        height: 19px;
        line-height: 19px;
        font-size: 17px;
        margin-left: 5px;
      }

      .top {
        left: 35%;
      }
    }

    &:nth-child(2n+2) {
      background-color: $menu-background
    }

    &:nth-child(2n+3) {
      background-color: #ffffff
    }

    &:nth-child(2n+1) {
      border-bottom: 2px solid $menu-border
    }

    &:last-child {
      border-bottom: none;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
    }
  }
}

.confirm-message {
  color: #36749d;
}

.fa-file-pdf-o, .fa-remove {
  float: right;
  margin-right: 5%;
  cursor: pointer;
  font-size: 18px;
  color: #e54c36;
}

.fa-pencil{
  float: right;
  margin-right: 5%;
  cursor: pointer;
  font-size: 18px;
  color: #a9a9a9;

}
.modifica {
  background: url("../../../../../assets/img/optimaIcons/modifica_color.png") no-repeat center;
  background-size: contain;
}

.icon {
  float: left;
  width: 65px;
  height: 65px;
  margin: 10px 15px 0 auto;
}

.app--btn-dropdown {
  padding-right: 0 !important;
  padding-left: 0 !important;
  width: 0;
  border: none;
  background: none !important;
}

.icons {
  margin: auto;
}

.info-modal {
  ::ng-deep .modal-window {
    .modal-header {
      border-bottom: 0;
    }
    .modal-content {
      height: 170px;
      width: 700px;
    }
  }

  .detail-row {
    margin-top: 15px;
    width: 100%;
  }
  .subject {
    width: 90%;
    color: $dark-blue;
  }
  .data {
    width: auto;
  }

  .modal-message {
    text-align: center;
    padding: 20px;
    margin-bottom: 40px;
    font-size: 18px;
    color: #36749d;
  }
}

@media screen and (max-width: 991px) {
  .info-modal {
    ::ng-deep .modal-window {
      .modal-dialog{
        width: 700px;
      }
      .modal-header {
        border-bottom: 0;
      }
      .modal-content {
        height: 170px;
        width: 700px;
      }
    }

    .detail-row {
      margin-top: 15px;
      width: 100%;
    }
    .subject {
      width: 90%;
    }
    .data {
      width: auto;
    }

    .modal-message {
      text-align: center;
      padding: 20px;
      margin-bottom: 40px;
      font-size: 18px;
      color: #36749d;
    }
  }
  .app--user-data {
    padding: 0;
  }
  .page-title {
    display: none;
  }
  .view img {
    width: 100%;
    padding: 0 0 15px 0;
  }

  .user-data-block {
    padding: 0;

    .client-name {
      font-size: 14px;

      ::ng-deep .form-block {
        font-size: 14px;
      }
    }
  }
  .user-data-table-block {
    .title {
      display: none;
    }


  }
}

@media screen and (max-width: 769px) {
  .info-modal {
    ::ng-deep .modal-window {
      .modal-dialog{
        width: 500px;
        margin: 30px auto;
      }
      .modal-header {
        border-bottom: 0;
      }
      .modal-content {
        height: 170px;
        width: 510px;
      }
    }

    .detail-row {
      margin-top: 15px;
      width: 100%;
    }
    .subject {
      width: 90%;
    }
    .data {
      width: auto;
    }

    .modal-message {
      text-align: center;
      padding: 20px;
      margin-bottom: 40px;
      font-size: 18px;
      color: #36749d;
    }
  }
}

@media screen and (max-width: 550px) {
  .info-modal {
    ::ng-deep .modal-window {
      .modal-dialog{
        width: 427px;
        margin: 30px auto;
      }
      .modal-header {
        border-bottom: 0;
      }
      .modal-content {
        height: 170px;
        width: 427px;
      }
    }

    .detail-row {
      margin-top: 15px;
      width: 100%;
    }
    .subject {
      width: 86%;
    }
    .data {
      width: auto;
    }

    .modal-message {
      text-align: center;
      padding: 20px;
      margin-bottom: 40px;
      font-size: 18px;
      color: #36749d;
    }
  }
  .user-data-table-block {
    .detail-row {
      ::ng-deep .fa {
        font-size: 14px;
      }

      ::ng-deep .form-block, .data {
        font-size: 12px;
      }

      div {
        padding-left: 5px;
        padding-right: 5px;
        font-size: 12px;
      }
    }
  }
}

@media screen and (max-width: 450px) {
  .info-modal {
    ::ng-deep .modal-window {
      .modal-dialog{
        width: 327px;
        margin: 30px auto;
      }
      .modal-header {
        border-bottom: 0;
      }
      .modal-content {
        height: 170px;
        width: 327px;
      }
    }

    .detail-row {
      margin-top: 15px;
      width: 100%;
    }
    .subject {
      width: 75%;
      font-size: 11px;
    }
    .data {
      width: auto;
    }

    .modal-message {
      text-align: center;
      padding: 20px;
      margin-bottom: 40px;
      font-size: 15px;
      color: #36749d;
    }
  }
}
