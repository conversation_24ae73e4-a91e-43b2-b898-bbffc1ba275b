import {Component, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, Validators} from '@angular/forms';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {VoucherCardService} from '../../../../common/services/voucher-card/voucher-card.service';
import {ContractsDetails, RedeemVoucher, TopUpSimByVoucher, VoucherInformation} from '../../model/RedeemVoucher';
import {ContractRecord} from '../../../../common/model/mobile/contract-record/ContractRecord';
import {MobileService} from '../../../../common/services/mobile/mobile.service';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';
import {PdfService} from '../../../../common/services/pdf/pdf.service';

@Component({
  selector: 'app-mobile-card',
  templateUrl: './mobile-card.component.html',
  styleUrls: ['./mobile-card.component.scss']
})
export class MobileCardComponent implements OnInit {

  contractRecords: Array<ContractRecord> = [];
  formGroup: FormGroup;
  formGroupChooseNumber: FormGroup;
  formGroupChooseContract: FormGroup;
  showBlockForChooseNumber: boolean;
  showBlockForChooseContract: boolean;
  showAlreadyUsedCard: boolean;
  totalSumVouchers: string;
  showWriteCardBlock = true;
  cardValue: number;
  showErrorWindow: boolean;
  showGeneralErrorWindow: boolean;
  showSuccessWindow: boolean;
  contracts: ContractsDetails[] = [];
  chosenContract: ContractsDetails;
  usedCardsInformation: VoucherInformation[] = [];
  isMediumDevices = window.innerWidth <= 1200;

  constructor(private voucherCardService: VoucherCardService, private formBuilder: FormBuilder, private mobileService: MobileService,
              private downloadFileService: PdfService, private incidentEventService: IncidentEventService) {
    this.voucherCardService.getInformationAboutUserCards('1').subscribe(response => {
      if (response.entities && response.entities.length > 0) {
        this.showAlreadyUsedCard = true;
        this.usedCardsInformation = response.entities;
        this.totalSumVouchers = this.usedCardsInformation.reduce((sum, item) => sum + item.creditoResiduo, 0).toFixed(2);
      }
    });
    this.formGroup = this.formBuilder.group({
      voucher: [null, Validators.required]
    });
    this.formGroupChooseNumber = this.formBuilder.group({
      msisdnId: [null, Validators.required]
    });
    this.formGroupChooseContract = this.formBuilder.group({
      option: [null, Validators.required]
    });
  }

  aggregateMobileContracts(contract: ContractsDetails) {
    if (contract.tipoContratto === 'MOBILE' && contract.tipoProdotto !== 'PS') {
      const existingContract = this.contracts.find(item => item.idGruppo === contract.idGruppo);
      if (existingContract) {
        this.contracts.forEach(item => {
          if (item.idGruppo === contract.idGruppo) {
            ['msisdn', 'indirizzoFornitura'].forEach(prop => {
              if (item[prop] && contract[prop]) {
                item[prop] += '; ' + contract[prop];
              } else if (!item[prop] && contract[prop]) {
                item[prop] = contract[prop];
              }
            });
          }
        });
      } else {
        this.contracts.push(contract);
      }
    }
  }

  ngOnInit() {
  }

  checkCode() {
    if (!this.formGroup.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    } else {
      this.voucherCardService.checkVoucherCard(this.formGroup.value.voucher).subscribe(result => {
        if (!result.entities || result.entities[0].stato !== 'DA_ASSOCIARE_CLIENTE' || result.entities[0].tipoCard !== 'Mobile Card') {
          this.showErrorWindow = true;
        } else {
          this.cardValue = result.entities[0].valore;
          this.mobileService.loadContractRecords(localStorage.getItem('clientId')).subscribe(item => this.contractRecords = item);
          this.showBlockForChooseNumber = true;
          this.showWriteCardBlock = false;
        }
      }, () => {
        this.showGeneralErrorWindow = true;
      });
    }
  }

  chooseNumber() {
    if (!this.formGroupChooseNumber.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupChooseNumber);
    } else {
      this.voucherCardService.getSimBalanceInformation(this.formGroupChooseNumber.value.msisdnId.substring(2)).subscribe(response => {
        if (response.response[0].StatoSim === 'Sim Da Fatturare') {
          this.voucherCardService.getContractsInformation().subscribe(contracts => {
            contracts.response.forEach(contract => {
              this.aggregateMobileContracts(contract);
            });
          });
          this.showBlockForChooseNumber = false;
          this.showBlockForChooseContract = true;
        } else {
          this.contractRecords.forEach(record => {
            if (record.msisdnId && record.msisdnId.toString(10) === this.formGroupChooseNumber.value.msisdnId) {
              const requestBodyRedeemVoucherTopUpSim = new RedeemVoucher(localStorage.getItem('clientId'), 'SelfCare', this.formGroup.value.voucher,
                parseInt(localStorage.getItem('clientId'), 10), response.response[0].IdContratto, true);
              this.voucherCardService.redeemVoucherCard(requestBodyRedeemVoucherTopUpSim).subscribe(informationResponse => {
                if (informationResponse.entities && informationResponse.entities[0].stato === 'UTILIZZATO') {
                  this.voucherCardService.redeemVoucherCardForTopUpSim(new TopUpSimByVoucher(record.id.toString(10),
                    this.formGroupChooseNumber.value.msisdnId, this.cardValue.toString())).subscribe(responseTopUp => {
                    if (responseTopUp) {
                      this.incidentEventService.openIncidentEventForRedeemVoucher(this.formGroup.value.voucher, 'MOBILE', this.cardValue).subscribe(responseIncident => {
                        if (responseIncident.status === ServiceResponseStatus.OK) {
                          this.showSuccessWindow = true;
                        } else {
                          this.showGeneralErrorWindow = true;
                        }
                      });
                    }
                  }, () => {
                    this.showGeneralErrorWindow = true;
                  });
                }
              }, () => {
                this.showGeneralErrorWindow = true;
              });
            }
          });
        }
      });
    }
  }

  chooseContract() {
    if (!this.formGroupChooseContract.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupChooseContract);
    } else {
      this.chosenContract = this.contracts.find(item => item.idContratto === parseInt(this.formGroupChooseContract.value.option, 10));
      const requestBodyRedeem = new RedeemVoucher(localStorage.getItem('clientId'), 'SelfCare', this.formGroup.value.voucher,
        parseInt(localStorage.getItem('clientId'), 10), parseInt(this.formGroupChooseContract.value.option, 10), false);
      this.voucherCardService.redeemVoucherCard(requestBodyRedeem).subscribe(response => {
        if (response.entities && response.entities[0].stato === 'DA_UTILIZZARE') {
          this.incidentEventService.openIncidentEventForRedeemVoucher(this.formGroup.value.voucher, 'MOBILE', this.cardValue).subscribe(responseIncident => {
            if (responseIncident.status === ServiceResponseStatus.OK) {
              this.showSuccessWindow = true;
            }
          });
        }
      });
    }
  }

  downloadFile(endUrl) {
    this.downloadFileService.downloadInvoicePDF(endUrl).subscribe(res => {
      const blob = new Blob([res], {type: 'application/pdf'});
      const data = window.URL.createObjectURL(blob);
      window.open(data, '_blank');
    });
  }

  hideModalWindow() {
    this.showErrorWindow = false;
  }

  reloadPage() {
    window.location.reload();
  }
}
