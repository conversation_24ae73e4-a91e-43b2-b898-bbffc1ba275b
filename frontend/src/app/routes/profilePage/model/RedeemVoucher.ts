import {ErrorStatus} from '../../../common/model/ChangePersonalDataResponse';

export class RedeemVoucher {
  user: string;
  system: string;
  codice: string;
  cliente: number;
  idContratto: number;
  isRicarica: boolean;

  constructor(user: string, system: string, codice: string, cliente: number, idContratto: number, isRicarica: boolean) {
    this.user = user;
    this.system = system;
    this.codice = codice;
    this.cliente = cliente;
    this.idContratto = idContratto;
    this.isRicarica = isRicarica;
  }
}

export class ShortVoucherInformation {
  codice: string;
  valore: number;
  stato: string;
  idCliente: number;
  idContratto: number;
  tipoCard: string;
  dataUltimaOperazione: string;
}

export class VoucherCardResponse {
  entities: ShortVoucherInformation[];
  esito: number;
  descrizioneEsito: string;
}

export class VoucherInformationResponse {
  entities: VoucherInformation[];
}

export class VoucherInformation {
  codiceCoupon: string;
  tipoCard: string;
  importCoupon: number;
  dataAttivazione: string;
  creditoResiduo: number;
  idClienteAssociato: number;
  idContrattoAssociato: number;
  idFatturaAssociato: number;
  dataStipula: string;
  indirizzo: string;
  pod: string;
  pdr: string;
  sim: string;
  numeroFattura: number;
  importoTipoCardInFattura: number;
  fatturaRiferimento: number;
  dataEmissione: string;
  downloadUrl: string;
  // additional filed
  showGeneralInformation: boolean;
  showContractsInDetails: boolean;
  showAgreementsInDetails: boolean;
}

export class VoucherContractsInformation {
  errorStatus: ErrorStatus;
  response: ContractsDetails[];
}

export class ContractsDetails {
  idContratto: number;
  utenza: string;
  idGruppo: number;
  dataStipula: string;
  spRelativeUri: string;
  idCliente: number;
  tipoContratto: string;
  tipoProdotto: string;
  POD: string;
  PDR: string;
  subscriptionId: string;
  msisdn: string;
  indirizzoFornitura: string;
  contractsDetailsAfterAggregation: ContractsDetailsAfterAggregation = new ContractsDetailsAfterAggregation();

  constructor(contract: ContractsDetails) {
    this.idContratto = contract.idContratto;
    this.utenza = contract.utenza;
    this.idGruppo = contract.idGruppo;
    this.dataStipula = contract.dataStipula;
    this.spRelativeUri = contract.spRelativeUri;
    this.idCliente = contract.idCliente;
    this.tipoContratto = contract.tipoContratto;
    this.tipoProdotto = contract.tipoProdotto;
    this.POD = contract.POD;
    this.PDR = contract.PDR;
    this.subscriptionId = contract.subscriptionId;
    this.msisdn = contract.msisdn;
    this.indirizzoFornitura = contract.indirizzoFornitura;
    this.contractsDetailsAfterAggregation.indirizzoFornitura.push(contract.indirizzoFornitura);
    this.contractsDetailsAfterAggregation.POD.push(contract.POD);
    this.contractsDetailsAfterAggregation.PDR.push(contract.PDR);
  }
}

export class ContractsDetailsAfterAggregation {
  indirizzoFornitura: string[] = [];
  POD: string[] = [];
  PDR: string[] = [];
}

export class VoucherSimBalanceInformation {
  errorStatus: ErrorStatus;
  response: SimDetail[];
}

export class SimDetail {
  SubscriptionId: number;
  IdContratto: number;
  Utenza: string;
  StatoSim: string;
}

export class TopUpSimByVoucher {
  operazioneDTO: OperazioneDTO;
  abstractKeyValueDTO: AbstractKeyValueDTO[];

  constructor(subscriptionId: string, msisdn: string, value: string) {
    this.operazioneDTO = new OperazioneDTO(subscriptionId, msisdn);
    this.abstractKeyValueDTO = [new AbstractKeyValueDTO(value)];
  }
}

export class OperazioneDTO {
  tipoOperazione = 'MOB_MODIFY_BALANCE_FRINGE';
  statoOperazione = 'MOB_NUOVA';
  tipoServizio = 'MOBILE';
  system = 'OPTIMA_MOBILE';
  subscriptionId: string;
  msisdn: string;

  constructor(subscriptionId: string, msisdn: string) {
    this.subscriptionId = subscriptionId;
    this.msisdn = msisdn;
  }
}

export class AbstractKeyValueDTO {
  key = 'amount';
  value: string;

  constructor(value: string) {
    this.value = value;
  }
}
