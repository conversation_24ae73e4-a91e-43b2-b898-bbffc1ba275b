export class ConfirmPaymentModelRequest {
  SistemaChiamante = 'Selfcare';
  CodiceCliente: string;
  CodificaCliente = 'OPT';
  CF: string;
  PIVA: string;
  TipoOperazione = 'ATTIV';
  URLNotify = '';
  URLError = '';
  URLCallbackChiam = '';
  IdOrdine = '';
  AddInfo1 = '';
  AddInfo2 = '';
  AddInfo3 = '';
}

export class ConfirmPaymentModelResponse {
  Esito: string;
  CodiceEsito: string;
  Descrizione: string;
  IdRisp: number;
  IdCliente: number;
  PaymentId: string;
  IdOrdine: string;
  APILicenseKeyEasy: string;
  SDK: string;
}

export class NexiPaymentRequest {
  SistemaChiamante = 'Selfcare';
  CodiceCliente: number;
  CodificaCliente = 'OPT';
  CF: string;
  PIVA: string;
  TipoOperazione = 'ATTIV_ADDEB';
  TipoPagamento = 'Ricariche';
  URL = '';
  URL_Back = '';
  URLCallbackChiam = '';
  IdOrdine = '';
  Importo: number;
  OneShot = 'Y';
  AddInfo1: string;
  AddInfo2: string;
  AddInfo3: string;
  Ricariche: Ricariche[] = [];

  constructor(CodiceCliente: number, CF: string, PIVA: string, Importo: number, AddInfo2: string, AddInfo3: string) {
    this.CodiceCliente = CodiceCliente;
    this.CF = CF;
    this.PIVA = PIVA;
    this.Importo = Importo;
    this.AddInfo1 = CodiceCliente.toString();
    this.AddInfo2 = AddInfo2;
    this.AddInfo3 = AddInfo3;
    this.Ricariche.push(new Ricariche(AddInfo3, Importo, AddInfo2));
  }
}

export class Ricariche {
  NumeroTelefono: string;
  Importo: number;
  SubscriptionID: string;
  ChannelId = '1';

  constructor(NumeroTelefono: string, Importo: number, SubscriptionID: string) {
    this.NumeroTelefono = NumeroTelefono;
    this.Importo = Importo;
    this.SubscriptionID = SubscriptionID;
  }
}

export interface NexiPaymentResponse {
  idResponse: number;
  esito: string;
  descrizione: string;
  urL_Form: string;
  alias: string;
  importo: string;
  divisa: string;
  codtrans: string;
  url: string;
  url_back: string;
  mac: string;
  num_contratto?: string;
  tipo_servizio?: string;
  tipo_richiesta?: string;
  urlpost: string;
  html: string;
}

export class ActivatePayPalAutoricaricaRequest {
  TipoPagamento = 'PayPal';
  SistemaChiamante = 'Selfcare';
  Data: string;
  TotalePagamento = 0;
  Vendite: null;
  Fatture: null;
  Dilazioni: null;
  Ricariche: null;
  CodiceCliente: string;
  RagioneSociale: string;
  PIVA: string;
  CF: string;
  Tokenize = 'Y';
  IdOrdineEsterno: string | null;
  UrlCallback: string | null;
  CodificaCliente = 'OPT';
  AddInfo1: string | null;
  AddInfo2: string | null;
  AddInfo3: string | null;
  TipoOperazione = 'Attivazione';
}

export class PayPalResponse {
  idResponse: number;
  esito: number;
  descrizione: string;
  dettaglio: string;
  returnURL: string;
  typePayment: string;
  clientId: number;
  idTransaction: number;
}
