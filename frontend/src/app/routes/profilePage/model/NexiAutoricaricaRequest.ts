export class NexiAutoricaricaRequest {
  SistemaChiamante = 'Selfcare';
  CodificaCliente = 'OPT';
  TipoOperazione = 'ATTIV';
  TipoPagamento = 'Attivazione';
  URL = '';
  URL_Back = '';
  URLCallbackChiam = '';
  IdOrdine = '';
  CodiceCliente: number;
  CF: string;
  PIVA: string;
  OneShot = 'N';
  AddInfo1: string;
  AddInfo2: string;
  AddInfo3: string;

  constructor(CodiceCliente: number, CF: string, PIVA: string, AddInfo1: string, AddInfo2: string, AddInfo3: string) {
    this.CodiceCliente = CodiceCliente;
    this.CF = CF;
    this.PIVA = PIVA;
    this.AddInfo1 = AddInfo1;
    this.AddInfo2 = AddInfo2;
    this.AddInfo3 = AddInfo3;
  }
}
