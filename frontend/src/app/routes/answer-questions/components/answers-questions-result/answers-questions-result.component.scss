@import "~app/shared/styles/colors";

.question-answer-result-layout {
  border: 1px solid $menu-border;
  border-radius: 5px;
  padding-top: 20px;
  padding-bottom: 20px;
  color: $dark-blue;

  .table-header {
    font-weight: bold;
    margin-top: 2%;
  }

  .answer-question-row {
    margin-top: 2%;
    float: left;
    width: 100%;
  }

  .table-header-mobile {
    display: none;
  }
}

.app-input {
  width: 60%;
  border-radius: 7px;
  border: 1px solid $menu-border;
  height: 40px;
  box-shadow: none;
  padding-left: 5px;
}

.app-select {
  border-radius: 7px;
  height: 40px;
  padding: 2px 25px;
  font-weight: 500;
  border-color: $menu-border;
  color: $dark-blue;
  -webkit-appearance: none; /*Removes default chrome and safari style*/
  -moz-appearance: none;
  background: url(/assets/img/icons/arrow-down_16x16.png) no-repeat right white;
  background-position-x: 95%;
}

.form-inline {
  text-align: left
}

@media screen and(max-width: 991px) {
  .question-answer-result-layout {
    .table-header-mobile, .question {
      font-weight: bold;
      margin-bottom: 2px;
    }

    .table-header-mobile {
      font-size: 12px;
      display: block;
    }

    .question-header {
      margin-top: 2%;
    }

    padding: 20px 0;

    .table-header {
      display: none;
    }

    .answer-question-row {
      margin-top: 0;
    }
  }
  .app-select, .app-input {
    border-radius: 3px;
    height: 20px;
    padding: 0 0 0 2px;
    width: 150px;
  }
}

@media screen and (max-width: 538px) {
  .question-answer-result-layout {
    .category {
      padding-right: 0;
    }

    .question-header {
      margin-top: 7%;
    }

    .app-select, .app-input {
      padding: 0 0 0 2px;
      width: 80%;
      font-size: 7px;
    }
  }
}

@media screen and (max-width: 370px) {
  .question-answer-result-layout {
    .category, .search {
      padding: 2px 0 0 25px;
      display: block;
      clear: left;
    }

    .question-header {
      margin-top: 7%;
    }

    .app-select, .app-input {
      padding: 0 0 0 2px;
      width: 150px;
      font-size: 7px;
      display: block;
    }
  }
}



