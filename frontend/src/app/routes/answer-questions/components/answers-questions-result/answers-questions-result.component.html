<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 question-answer-result-layout">
  <div *ngFor="let service of services">
    <div *ngIf="settingActive == service.activeNumber">
      <div class="questions-answers">
        <div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 category">
          <div *ngIf="categories[service.activeNumber].length != 0" class="category-container">
            <label for="category">CATEGORIA:</label>
            <select [ngModelOptions]="{standalone: true}" id="category" class="app-select"
                    [(ngModel)]="selected[service.activeNumber]" (change)="filterData()">
              <option *ngFor="let category of categories[service.activeNumber]"
                      [ngValue]="category">{{category}}
              </option>
            </select>
          </div>
        </div>
        <div class="col-lg-7 col-md-7 col-sm-6 col-xs-6 search">
          <label for="search">RICERCA:</label>
          <input type="text" [(ngModel)]="searchValue" [ngModelOptions]="{standalone: true}"
                 class="app-input" (input)="filterData()" id="search">
        </div>
        <div class="col-lg-5 col-md-5 table-header">
          Domanda
        </div>
        <div class="col-lg-7 col-md-7 table-header">
          Risposta
        </div>
        <div *ngFor="let row of datatable1_rows" class="answer-question-row">
          <div class="col-lg-5 col-md-5 table-header-mobile question-header">
            Domanda
          </div>
          <div class="col-lg-5 col-md-5 question">{{ row.FIELD5 }}</div>
          <div class="col-lg-7 col-md-7 table-header-mobile">
            Risposta
          </div>
          <div class="col-lg-7 col-md-7">{{ row.FIELD6 }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
