import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AnswerQuestionsComponent } from './questions/answer-questions.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { QuestionModulesComponent } from './questionModules/question-modules.component';
import { AnswersQuestionsResultComponent } from './components/answers-questions-result/answers-questions-result.component';
import {ResolutionCheckerGuard} from './utils/resolution-checker.guard';


const routes: Routes = [
  {
    path: 'questions', component: AnswerQuestionsComponent, children: [
      {path: '', canActivate: [ResolutionCheckerGuard]},
      {path: 'assicurazioni', component: AnswersQuestionsResultComponent},
      {path: 'device', component: AnswersQuestionsResultComponent},
      {path: 'ee', component: AnswersQuestionsResultComponent},
      {path: 'gas', component: AnswersQuestionsResultComponent},
      {path: 'conto-relax', component: AnswersQuestionsResultComponent},
      {path: 'pi', component: AnswersQuestionsResultComponent},
      {path: 'voce-adsl', component: AnswersQuestionsResultComponent},
      {path: 'fattura', component: AnswersQuestionsResultComponent},
      {path: 'info-generice', component: AnswersQuestionsResultComponent},
      {path: 'faq', component: AnswersQuestionsResultComponent},
      {path: 'amazonprime', component: AnswersQuestionsResultComponent},
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    AnswerQuestionsComponent,
    QuestionModulesComponent,
    AnswersQuestionsResultComponent
  ],
  providers: [ResolutionCheckerGuard]
})

export class AnswerQuestionsModule {
}
