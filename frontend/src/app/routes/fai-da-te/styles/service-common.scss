@import "../../../shared/styles/colors";
@import "../../../shared/styles/app.mat-dropdown";

.outBox {
  border: 1px solid $menu-border;
  border-radius: 5px;
  background-color: #ffffff;
}

.topBlock {
  display: flex;
}

.mat-menu-style {
  width: 450px;
}

.app--header-block {
  display: flex;
  align-items: center;
  margin-left: 2%;
  margin-top: 1%;

  .topBlock {
    float: none;
    display: inline-block;
    height: 70px;
    width: 50px;
    padding-left: 5px;
  }

  .serviceName {
    display: inline-block;
    padding-top: 5px;
    padding-left: 25px;
  }
}

.serviceName {
  color: $dark-blue;
  font-weight: bold;
  font-size: 26px;
}

.mainBlock {
  color: $dark-blue;
  margin-top: 1%;
  border-radius: 5px;
  border: 1px solid $menu-border;
  background: $menu-background;
  padding: 10px;

  > .row p {
    padding-right: 60px;
  }
}

.mainBlock p {
  line-height: 15px;
}

.mainBlock {
  position: relative;
}


.addition {
  color: #b6cce3;
}

.serviziButton {
  margin-top: 2%;
  margin-bottom: 2%;
}

.elenco {
  border-radius: 5px;
  border: 1px solid $dark-blue;
  padding: 5px 10px;
  color: $dark-blue;
  display: inline-block;
}

.addition {
  color: #b6cce3;
}

.mobileView {
  display: none;
}

// Dropdown
.dropdown {
  position: absolute;
  top: 1%;
  right: 1%;
  opacity: 1;
}

.dontShow {
  height: 0;
}

.icon {
  width: 65px;
  height: 65px;
  margin: 10px auto 0 auto;
  text-align: right;
  float: right;
  position: absolute;
  right: 5%;
}

.disabled {
  pointer-events: none;
}

@media only screen and (max-width: 991px) {
  .block {
    width: 90%;
    margin: auto;
  }

  .block-md-3 {
    width: 25%;
    float: left;
  }
  .block-md-9 {
    width: 75%;
    float: left;
  }
  .container-fluid {
    width: 100%;
    padding: 0;
  }
  .mat-menu-style {
    width: 450px;
  }
}


@media only screen and (max-width: 768px) {
  .url {
    margin-top: 15px;

    a {
      padding: 8px 0;
      color: #000;
    }

    a:hover {
      text-decoration: underline;
    }
  }
  .mainBlock > .row p {
    padding-right: 0;
  }
  .modificaPDF {
    clear: both;
    display: none;
  }
  .show {
    display: block;
  }

  .pdflist {
    line-height: 15px;
    padding: 5px 0;

    a {
      color: #36749d;
      text-decoration: none;
    }
  }
  // dropdown
  .app-drop-down {
    padding-left: 250px;
    height: 35px;
    text-align: center;
  }
  .dropdown {
    position: relative;
  }
  .serviziButton {
    text-align: center;
  }
  .icon {
  }
  .modifica {
    background: #ffffff none;
    border: 1px solid $dark-blue;
    font-size: 18px;
    height: auto;
    alignment: center;
    width: 275px;
    border-radius: 5px;
    position: absolute;
    top: -30px;
    text-align: center;
  }
  .modifica:after {
    content: "Modifica";
  }

  ::ng-deep.cdk-overlay-container {
    margin-left: 30px;
  }

  .mat-menu-style {
    width: 450px;
  }

  ::ng-deep div.mat-menu-panel {
    overflow: auto;
    height: 250px;
    width: 280px;

    background: white;
    margin-top: 15px;
  }
}

@media only screen and (max-width: 400px) {
  .app-drop-down  {
    padding-left: 228px;
    .modifica {
      width: 250px;
    }
  }
}

@media only screen and (max-width: 380px) {
  .app-drop-down  {
    padding-left: 175px;
    .modifica {
      width: 200px;
    }
  }
}
