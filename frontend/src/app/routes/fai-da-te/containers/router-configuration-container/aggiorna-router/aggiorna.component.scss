@import "~app/shared/styles/colors";
.main {
  width: 90%;
  margin: auto;
  border-radius: 10px;
  border: 1px solid $menu-border;
  padding-bottom: 20px;
}

.scarica {
  clear: both;
  padding: 18px 0 !important;
}

.textScarica {
  position: relative;
}

.container {
  width: 95%;
  margin: auto;
}

.button {
  text-decoration: none;
  padding: 2px 5px;
  border-radius: 5px;
  color: $dark-blue;
  text-align: center;
  border: 1px solid $dark-blue;
  margin: 0 15px 15px 0;
  font-size: 15px;
  cursor: default;
}

.button:hover, .button:active {
  background: $dark-blue;
  color: white;
}

.button:hover .icon-pdf-load, .button:active .icon-pdf-load {
  color: white;
}

.header {
  border-bottom: 1px solid $menu-border;
  margin-bottom: 15px;
}

.content {
  padding: 0 30px;
  color: $dark-blue;
}

.title {
  color: $dark-blue;
  padding: 10px 2.5%;
  font-weight: 400;
  font-size: 25px;
}

.name {
  padding: 10px;
  color: $dark-blue;
  font-size: 15px;
}

.icon-pdf-load {
  font-size: 35px;
}

input {
  padding: 5px;
  border-radius: 5px;
  border: 1px solid $menu-border;
}

.col-md-3 {
  margin: 5px 0;
}

.box-title {
  padding: 5px 10px;
}

.sub-block {
  margin-top: 20px;
}

.sub-title {
  font-size: 17px;
}

.left-padding {
  margin-left: 0;
}

.numero {
  line-height: 40px;
  padding: 10px 2.5%;
  color: $dark-blue;
  font-size: 16px;
}

.link {
  text-decoration: underline;
  color: $dark-blue;
  display: block;
  clear: both;
  cursor: default;
}

.text {
  cursor: default;
  position: relative;
  top: -10px;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.control-block {
  padding: 0;
}

.label {
  color: $dark-blue;
  font-size: 18px;
  padding-left: 0;
}

.app-select{
  width: 100%;
}

.download-buttons {
  margin-top: 2%;
  float: left;
  width: 100%;
}

.info-block {
  position: relative;
  float: left;
  margin: 7px;
}

.info-circle {
  border-radius: 50%;
  border: 1px solid;
  height: 20px;
  width: 20px;
  text-align: center;
  font-style: italic;
  line-height: 20px;
  font-family: serif;
  font-weight: bold;
  font-size: 17px;
  display: inline-block;
  color: $dark-blue;
  cursor: pointer;
}

.justec-router {
  background: url("/assets/img/router/Justec.jpg") no-repeat;
  background-size: contain;
}

.adb-router {
  background: url("/assets/img/router/adb.png") no-repeat;
  background-size: contain;
}

.router-info {
  .router-title {
    font-weight: bold;
    color: $dark-blue;
    text-align: left;
  }

  ::ng-deep.top {
    width: 300px;
  }

  .justec-router, .adb-router {
    width: 100%;
    height: 12vh;
  }
}

@media screen and (max-width: 991px) {
  .content {
    padding: 0;
  }
}
@media screen and (max-width: 767px) {
  .info-block {
    width: 100%;
    text-align: center;
  }
}

