///<reference path="../../../../../common/services/fisso/fisso.service.ts"/>
import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {PodDetail} from '../../../../../common/model/fisso/PodDetail';

@Component({
  selector: 'app-voip-adslrouter',
  templateUrl: './voip-adslrouter.component.html',
  styleUrls: ['./voip-adslrouter.component.scss']
})
export class VoipAdslrouterComponent implements OnInit, OnChanges {
  @Input('username') username: string;
  @Input('password') password: string;
  @Input('adslId') adslId: string;
  @Input('voipData') voipData: Array<PodDetail>;

  constructor() {
  }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges): void {
  }

}
