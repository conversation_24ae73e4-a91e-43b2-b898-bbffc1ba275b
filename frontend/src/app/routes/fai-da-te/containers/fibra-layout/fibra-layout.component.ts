import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { select } from '@angular-redux/store';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { Utility } from '../../../../common/model/services/userServices.model';
import { ServiziAttiviService } from '../../../../common/services/servizi-attivi/servizi-attivi.service';
import { OptimaIconUtils } from '../../../../common/utils/OptimaIconUtils';
import { HomeService } from '../../../home/<USER>/home/<USER>';
import { UserData } from '../../../../common/model/userData.model';
import { ObservableUtils } from '../../../../common/utils/ObservableUtils';
import ServiceStateModel from '../../../../redux/model/ServiceStateModel';
import { decodeInternetType } from '../../../../common/utils/InternetServiceUtils';
import { ServicesActions } from '../../../../redux/services/actions';

@Component({
  selector: 'app-fibra-layout',
  templateUrl: './fibra-layout.component.html',
  styleUrls: ['./fibra-layout.component.scss']
})
export class FibraLayoutComponent implements OnInit, OnDestroy {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['services'])
  serviceData: Observable<ServiceStateModel>;

  @select(['services', 'activeAdslPodDetails'])
  internetPodDetails: Observable<object>;

  internetPodDetailsSubscription: Subscription;
  userInfoSubscription: Subscription;
  serviceDataSubscription: Subscription;

  decodeType = decodeInternetType;

  userCluster: string;

  podDetails: object;

  pod: string;

  pdf: Array<any>;

  utenza: Utility = {} as Utility;

  serviceName = 'VOIP';

  constructor(private route: ActivatedRoute, private serviziAttiviService: ServiziAttiviService,
              private optimaIconUtils: OptimaIconUtils, private homeServices: HomeService,
              private servicesActions: ServicesActions) {
    this.userInfoSubscription = this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
        this.pdf = this.setPDFList('VOIP');
      }
    });
    this.internetPodDetailsSubscription = this.internetPodDetails.subscribe(details => {
      this.podDetails = details;
    });
    this.route.params.subscribe(params => {
      if (params.pod) {
        this.pod = params.pod;
      }
    });
    this.serviceDataSubscription = this.serviceData.subscribe(serviceState => {
      const {activeServices, servicesLoaded} = serviceState;
      if (servicesLoaded) {
        this.servicesActions.loadInternetPodDetailsIfNotExist();
      }
      Object.keys(activeServices).forEach(key => {
        activeServices[key].utilities.forEach(utility => {
          if (utility.utNumber === this.pod) {
            this.serviceName = activeServices[key].serviceName;
            this.utenza = utility;
          }
        });
      });
    });
  }

  ngOnInit() {
    window.scrollTo(0, 0);
  }

  serviceIcon(icon: string): string {
    return this.optimaIconUtils.getServiceIconByName(icon);
  }

  getName(name) {
    return this.serviziAttiviService.getNumberName(name);
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.internetPodDetailsSubscription, this.userInfoSubscription,
      this.serviceDataSubscription]);
  }

}
