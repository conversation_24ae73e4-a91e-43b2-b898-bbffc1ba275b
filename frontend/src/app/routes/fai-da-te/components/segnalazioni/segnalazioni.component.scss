@import "~app/shared/styles/colors";

.app-segnalazioni {
  color: $dark-blue;

  .mobile-header {
    display: none;
  }

  .page-title {
    background: #ffffff;
    border: 2px solid $menu-border;
    border-radius: 5px;
    height: 45px;

    .title-image {
      background: url("/assets/img/optima/Set_Icone_AreaClienti_Segnalazioni2.png") no-repeat center;
      background-size: contain;
      width: 68px;
      height: 50px;
      float: left;
    }

    .title-text {
      color: $dark-blue;
      margin-top: 11px;
      float: left;
    }
  }

  textarea {
    resize: none;
  }

  .invia {
    margin-top: 2%;
    padding-top: 10px;
    padding-bottom: 10px;
    border: 2px solid $menu-border;
    border-radius: 5px;

    .title {
      font-size: 16px;
    }

    .oggetto, .mes {
      border-color: $menu-border;
      border-radius: 5px;
    }

    .mes {
      width: 100%;
      min-height: 100px;
    }
  }

  button[disabled] {
    color: grey;
    border-color: grey;
  }

  .invia-segnalazioni {
    margin-top: 1%;
    border: 2px solid #c8d9e9;
    border-radius: 5px;
    font-weight: 600;
  }

  .margin-right-10 {
    margin-right: 10px;
  }

  .signal-data-table {
    border: 2px solid $menu-border;
    border-radius: 5px;
    margin-top: 2%;

    .table-header {
      font-weight: bold;
      font-size: 16px;
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .line {
      height: 1px;
      border-bottom: 1px solid $menu-border;
    }

    .signal-row {
      padding-top: 7px;
      padding-bottom: 7px;

      &:nth-child(odd) {
        background-color: $menu-background;
      }

      .default-app-button {
        padding: 0 5px;
      }

      &:last-child {
        margin-bottom: 2%;
      }
    }

  }

  /*------ notification -----*/
  .notification {
    position: fixed;
    right: 30px;
    bottom: 70px;
    width: 300px;
    padding: 10px;
    background: $menu-background;
    border-radius: 5px;
    color: $dark-blue;
    z-index: 2000;
    display: none;
    border: 1px solid $menu-border;
    text-align: center;
  }

  .riapri {
    top: 30%;
    z-index: 1000;
    padding: 10px;
    position: fixed;
    width: 600px;
    left: calc(50% - 200px);
    min-height: 350px;
    background: $menu-background;
    border-radius: 10px;
    border: 1px solid $menu-border;
    //display: none;

    .title {
      font-weight: bold;
      font-size: 16px;
    }

    .mes {
      width: 100%;
      min-height: 220px;
      border: 1px solid $menu-border;
      border-radius: 5px;
      margin-top: 2%;
    }

    .close {
      float: right;
      color: $dark-blue;
      padding: 10px;
      font-size: 10px;
      margin-top: -10px;
    }
  }

  .inserisci {
    top: 30%;
    z-index: 1000;
    padding: 10px;
    position: fixed;
    width: 600px;
    left: calc(50% - 200px);
    min-height: 350px;
    background: $menu-background;
    border-radius: 10px;
    border: 1px solid $menu-border;
    //display: none;

    .title {
      font-weight: bold;
      font-size: 16px;
    }

    .mes {
      width: 100%;
      min-height: 220px;
      border: 1px solid $menu-border;
      border-radius: 5px;
      margin-top: 2%;
    }

    .close {
      float: right;
      color: $dark-blue;
      padding: 10px;
      font-size: 10px;
      margin-top: -10px;
    }
  }

  .flex-width-left-part {
    width: 20%
  }

  .flex-width-right-part {
    width: 80%
  }

  .flex-width-left-part-email {
    width: 10%
  }

  .flex-width-right-part-email {
    width: 90%
  }

  #files-upload, #files-upload-riapri, #file-upload-inserisci {
    opacity: 0;
    position: absolute;
    z-index: -1;
  }

  .label-left {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 0;
  }

  .file-name {
    float: left;
    margin-left: 10px;
    max-width: 90%;
    overflow: hidden;
  }

  .file-wrong {
    float: left;
    margin-left: 10px;
    color: $coral;
    max-width: 90%;
    overflow: hidden;
  }

  .side-file-name {
    float: left;
  }

  .remove-file-button {
    border: none;
    font-size: 14px;
    background: none;
  }
}

@media screen and (max-width: 1600px) {
  .app-segnalazioni {
    .flex-width-left-part-email {
      width: 20%
    }

    .flex-width-right-part-email {
      width: 80%
    }
  }
}

@media screen and (max-width: 1280px) {
  .app-segnalazioni {
    .table-header {
      padding-right: 5px;
      padding-left: 5px;
    }

    .signal-row {
      div {
        padding-right: 5px;
        padding-left: 5px;
      }

      .default-app-button {
        font-size: 12px;
      }
    }
  }
}

@media screen and (max-width: 1085px) {
  .app-segnalazioni {
    .signal-data-table {
      .table-header {
        padding-right: 5px;
        padding-left: 5px;
        font-size: 14px;
      }
    }

    .signal-row {
      font-size: 12px;

      div {
        padding-right: 5px;
        padding-left: 5px;
      }

      .default-app-button {
        font-size: 10px;
      }
    }
  }
}

@media screen and (max-width: 991px) {
  .app-segnalazioni {
    padding: 0;

    .page-title {
      display: none;
    }

    .invia {
      background-color: #ffffff;
    }

    .signal-data-table {
      background-color: #ffffff;

      .table-header {
        padding-right: 5px;
        padding-left: 5px;
        font-size: 14px;
      }
    }

    .signal-row {
      font-size: 14px;

      div {
        padding-right: 5px;
        padding-left: 5px;
      }

      .default-app-button {
        font-size: 12px;
      }
    }

    .riapri {
      width: 550px;
      left: calc(50% - 300px);
    }

    .inserisci {
      width: 550px;
      left: calc(50% - 300px);
    }
  }
}

@media screen and (max-width: 767px) {
  .app-segnalazioni {
    .mobile-header {
      display: block;
      margin-top: 2%;
      font-weight: bold;
    }

    .signal-data-table {
      padding: 5px;

    }

    .table-header, .line {
      display: none;
    }

  }
}


@media screen and (max-width: 660px) {
  .app-segnalazioni {
    .riapri {
      width: 400px;
      left: calc(50% - 200px);
    }

    .inserisci {
      width: 400px;
      left: calc(50% - 200px);
    }

    .file-name {
      max-width: 70%;
    }

    .file-wrong {
      max-width: 70%;
    }

    .flex-width-left-part {
      width: 30%
    }

    .flex-width-right-part {
      width: 70%
    }

    .flex-width-left-part-email {
      width: 30%
    }

    .flex-width-right-part-email {
      width: 70%
    }

  }
}

@media screen and (max-width: 410px) {
  .app-segnalazioni {
    .riapri {
      width: 340px;
      left: auto;
    }

    .inserisci {
      width: 340px;
      left: auto;
    }

    .flex-width-left-part-email {
      width: 35%
    }

    .flex-width-right-part-email {
      width: 75%
    }
  }
}
