import {Component, OnInit, ViewChild} from '@angular/core';
import {<PERSON><PERSON><PERSON>y, FormBuilder, FormGroup} from '@angular/forms';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';

@Component({
  selector: 'app-autolettura',
  templateUrl: './autolettura.component.html',
  styleUrls: ['./autolettura.component.scss']
})
export class AutoletturaComponent implements OnInit {
  @select(['services', 'services'])
  services: Observable<Array<any>>;
  @ViewChild('gasPopup') gasPopup: any;
  // Luce
  luceContatoreList: any[] = [];
  luceContatoreTypeList: any[] = [];
  public luceFormGroup: FormGroup;
  // Gas
  gasContatoreList: any[] = [];
  gasContatoreTypeList: any[] = [];
  gasFormGroup: FormGroup;

  selectedLuceContatoreType: any;
  selectedGasContatoreType: any;

  constructor(private fb: FormBuilder) {
  }

  ngOnInit() {
    this.initComponent();
    this.formBuild();
  }

  private formBuild(data: any = null): void {
    this.luceFormGroup = this.fb.group({
      'contatore': [data && data.contatore],
      'contatoreType': [data && data.contatoreType],
      'values': new FormArray([])
    });

    this.gasFormGroup = this.fb.group({
      'contatore': [data && data.contatore],
      'contatoreType': [data && data.contatoreType],
      'values': new FormArray([])
    });

    // subscribers
    this.subscribeOnChanges();
  }

  private subscribeOnChanges(): void {
    // luce
    const luceContatoreType = this.luceFormGroup.controls['contatoreType'] as FormArray;
    luceContatoreType.valueChanges.subscribe((data: any) => {
      this.selectedLuceContatoreType = data !== '' ? data : null;
    });
    const luceContatore = this.luceFormGroup.controls['contatore'] as FormArray;
    luceContatore.valueChanges.subscribe((data: any) => {
      if (!data) {
        this.luceFormGroup.controls['contatoreType'].setValue(null);
      }
    });
    // gas
    const gasContatoreType = this.gasFormGroup.controls['contatoreType'] as FormArray;
    gasContatoreType.valueChanges.subscribe((data: any) => {
      this.selectedGasContatoreType = data !== '' ? data : null;
    });
    const gasContatore = this.gasFormGroup.controls['contatore'] as FormArray;
    gasContatore.valueChanges.subscribe((data: any) => {
      if (!data) {
        this.gasFormGroup.controls['contatoreType'].setValue(null);
      }
    });
  }

  private initComponent() {
    this.services.subscribe(
      (response: any[]) => {
        const that = this;
        response.forEach(function (data) {
          if (data.serviceName.toUpperCase() === 'ENERGIA') {
            that.luceContatoreList = data.utilities.map((utility: any) => {
              return { title: 'POD ' + utility.utNumber, value: utility.utNumber };
            });
          }
          if (data.serviceName.toUpperCase() === 'GAS') {
            that.gasContatoreList = data.utilities.map((utility: any) => {
              return { title: 'PDR ' + utility.utNumber, value: utility.utNumber };
            });
          }
        });
      }
    );
    this.luceContatoreTypeList = [
      { title: 'Elettronico', value: 'Elettronico' },
      { title: 'Meccanico', value: 'Meccanico' }
    ];
    this.gasContatoreTypeList = [
      { title: 'Senza correttore', value: 'Senza correttore' },
      { title: 'Con correttore', value: 'Con correttore' }
    ];
  }

}
