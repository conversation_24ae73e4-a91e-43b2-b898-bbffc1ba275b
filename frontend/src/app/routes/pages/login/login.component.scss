.center-block {
  text-align: center;
}

.header-font-size {
  font-size: 30px;
  margin-bottom: 10%;
  margin-top: 10%;
}

// Main wrapper
.wrapper {
  width: 100%;
  height: auto;
  min-height: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  background: transparent linear-gradient(91deg, #00B8FF 0%, #0035AD 100%) 0 0 no-repeat padding-box;
  color: white;
}

.btn-primary {
  background: #97B825 0 0 no-repeat padding-box;
  border: 1px solid #FFFFFF;
  border-radius: 100px;
  width: 35%;
  height: 15%;
  font-size: 14px;
  font-weight: bold;
  margin: 8% auto 0;
}

.register-button {
  width: 65%;
  height: 65px;
  font-weight: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.forgetPassword {
  color: white;
  text-decoration: underline;
  font: italic normal normal 17px Lato;
  text-align: center;
  cursor: pointer;
  margin-top: 3%;
}

.flex-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  height: 645px;

  .left-part-flex {
    display: flex;
    flex-direction: column;
    width: 29%;
    margin-top: 2%;

    .logo {
      height: 115px;
      background-size: contain;
      background: url("/assets/img/logo/optima_white_logo.svg") no-repeat center;
    }

    .servizi-picture {
      height: 410px;
      background: url("/assets/img/Servizi_login.png") no-repeat;
      background-size: contain;
    }
  }

  .line {
    border: 0.5px solid #73b4e8;
    height: 450px;
    background-color: #73b4e8;
  }

  .right-part-flex {
    display: flex;
    flex-direction: column;
    height: inherit;
    justify-content: center;
    width: 29%;

    .field-error {
      margin-top: 5%;
      margin-bottom: 5%;
      display: flex;
      flex-direction: column;

      .text-span-danger {
        color: #c4c4c4;
        margin-bottom: -3%;
        margin-left: 3%;
      }
    }

    .input-field {
      background: #2689f0 0 0 no-repeat padding-box;
      border-radius: 10px;
      border: 0;
      height: 55px;
      padding: 5%;
      font-size: 17px;
    }

    // Style for autocomplete
    input:-webkit-autofill {
      border: 0;
      -webkit-text-fill-color: white;
      transition: background-color 5000s ease-in-out 0s;
    }

    ::placeholder {
      color: white;
    }

    .text-style {
      color: #FF9300;
      font-size: 16px;
    }
  }
}

@media only screen and (max-width: 1380px) and (min-width: 980px){
  .register-button {
    width: 85%;
    font-size: 13px;
  }
}

@media only screen and (max-width: 980px) and (min-width: 761px){
  .register-button {
    width: 94%;
    font-size: 11px;
  }
}

@media only screen and (max-width: 760px) {
  .hide-block {
    display: none !important;
  }
  .flex-panel {
    height: 600px;

    .right-part-flex {
      width: 70%;
    }
  }
}

@media only screen and (max-width: 570px) {
  .register-button {
    font-size: 12px;
  }
}

@media only screen and (max-width: 480px) {
  .register-button {
    font-size: 10px;
    width: 85%;
  }
}

.modal-text {
  text-align: center;
  color: #36749d;
  width: 64%;
  margin: auto;
  padding-top: 25px;
  font-size: 20px;
}

.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 30px;
  border: 2px solid #b1c5df;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 560px;
}

.display {
  display: block;
}

.fa-times {
  position: absolute;
  right: 20px;
  font-size: 50px;
  top: 15px;
  color: #36749d;
  cursor: pointer;
}

.users {
  border: 2px solid #b1c5df;
  border-radius: 10px;
  text-align: center;
  width: 50%;
  color: #36749d;
  cursor: pointer;
  margin: 5% auto auto;
  padding: 3%;
  font-size: 15px;
}

.modal-image {
  text-align: center;
  background-color: white;
  margin: 3% auto auto;
  width: 20%;
  display: block;
}
