<nav>
  <div class="back-button" *ngIf="!showMenu" (click)="backButton()">
    <img src="/assets/img/icons/back_arrow.png" alt="Back"/>
  </div>
  <div class="logo-container">
    <img src="/assets/img/logo/optima_new_main_logo.svg" class="logo" alt="Logo"/>
  </div>
</nav>
<div class="wrapper-block">
  <div class="information-block">
    <h1 class="text-center">Benvenuto in Optima!</h1>
    <div *ngIf="!isMobile" class="text text-center">Qui troverai tutti gli aggiornamenti, in tempo reale, sullo stato di
      lavorazione del tuo contratto Optima.
    </div>
    <div *ngIf="isMobile && !selectedContract" class="text text-center">Seleziona uno dei servizi richiesti per vedere
      lo stato di lavorazione del tuo contratto.
    </div>
    <div *ngIf="isMobile && selectedContract" class="text text-center">
      Qui troverai tutti gli aggiornamenti, in tempo reale, sul servizio {{getServiceFromIcona(selectedContract.icona).toUpperCase()}}
    </div>
    <div class="service-menu" *ngIf="showMenu">
      <ng-container *ngFor="let service of this.services">
        <img class="service-image" [src]="getImageSrc(service)" alt="{{service}}"
             (click)="selectContract(getContractForService(service))"
             (mouseover)="onMouseOver(service)"
             (mouseout)="onMouseOut(service)">
      </ng-container>
    </div>
<!--    <div class="span-energy-text" *ngIf="selectedContract && selectedContract.icona === 'EE'">
      <div class="header">Qui troverai tutti gli aggiornamenti, in tempo reale, sul servizio LUCE.</div>
      POD XXXXXXXXXXXXXXXXX - indirizzo di fornitura: via Nome Via, XX, Città (XX), 000000
    </div>-->
    <div
      class="{{showLayoutWithCCN || showLayoutWithClientId || selectedContract?.motivazioneQCCKO === 1 ? 'pointer' : ''}}"
      (click)="openModalWindow()" *ngIf="(isMobile && !showMenu) || (!isMobile)">
      <img *ngIf="showLayoutWithClientId" class="img-layout"
           [src]="getImageLayoutSrc('Sfondo_8.png')" alt="Layout"/>
      <img
        *ngIf="!selectedContract" class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_0.png')" alt="Layout"/>
      <img
        *ngIf="showLayoutWithAzioneQCC && selectedContract.azioneQCC === 'CQCC'" class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_5.png')" alt="Layout"/>
      <img
        *ngIf="(showLayoutWithAzioneQCC && selectedContract.motivazioneQCCKO === 1 &&
        (selectedContract.attivazioneParziale)) || showLayoutWithFlagContrattoCestinato"
        class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_2.png')" alt="Layout"/>
      <img
        *ngIf="showLayoutWithCCN" class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_4.png')" alt="Layout"/>
      <img
        *ngIf="showLayoutWithDates && selectedContract.dataArrivoFax === null && selectedContract.dataImportazione === null"
        class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_1.png')" alt="Layout"/>
      <img
        *ngIf="showLayoutWithAzioneQCC && (selectedContract.azioneQCC === 'NOQCC' || selectedContract.azioneQCC === 'OKQCC'
        || selectedContract.azioneQCC==='QCCOK') || (showLayoutWithDates && selectedContract.dataArrivoFax !== null
        && selectedContract.dataImportazione === null) || (showLayoutWithAttivazioneParziale && !showLayoutWithFlagContrattoCestinato)"
        class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_3.png')" alt="Layout"/>
      <!--      <img *ngIf="showLayoutWithFlagContrattoCestinato" src="assets/img/register-status-layout/Sfondo_6.png"
                 class="img-layout" alt="Layout"/>-->
      <img
        *ngIf="showLayoutWithDates && selectedContract.dataArrivoFax !== null && selectedContract.dataImportazione !== null"
        class="img-layout"
        [src]="getImageLayoutSrc('Sfondo_8.png')" alt="Layout"/>
    </div>
    <button *ngIf="!showMenu" class="revert-button" (click)="backButton()">TORNA INDIETRO</button>
    <div
      *ngIf="(selectedContract?.attivazioneParziale === 1 && !isMobile) ||
      (contracts[0]?.attivazioneParziale === 1 && isMobile && showMenu)">
      <hr>
      <div class="span-text-below font-weight-bold">
        <img src="assets/img/icons/exclamation-point.png" class="alert-picture" alt="Warning">
        <div>Ti informiamo che non è stato possibile procedere con l’attivazione dei servizi:</div>
      </div>
      <div *ngIf="selectedContract?.attivazioneParziale === 1 && !isMobile"
           class="font-weight-bold text-center font-size-text margin">{{selectedContract.serviziCestinati}}</div>
      <div *ngIf="isMobile"
           class="font-weight-bold text-center font-size-text margin">{{contracts[0].serviziCestinati}}</div>
      <div class="font-weight-bold text-center font-size-text">La procedura di attivazione continuerà per gli altri
        servizi presenti nel tuo contratto
      </div>
    </div>
    <div
      *ngIf="(selectedContract?.attivazioneParziale === 0 && !isMobile) ||
      (contracts[0]?.attivazioneParziale === 0 && isMobile && showMenu)">
      <hr>
      <div class="span-text-below font-weight-bold">
        <img src="assets/img/icons/exclamation-point.png" class="alert-picture" alt="Warning">
        <div *ngIf="selectedContract?.attivazioneParziale === 0 && !isMobile" style="width: 59%">Ti informiamo che non è
          stato possibile procedere con l’attivazione dei servizi previsti
          dal contratto stipulato in data {{selectedContract.dataStipula | date:'dd/MM/yyyy'}}
        </div>
        <div *ngIf="isMobile" style="width: 59%">Ti informiamo che non è stato possibile procedere con l’attivazione dei
          servizi previsti dal contratto stipulato in data:
        </div>
      </div>
      <div *ngIf="isMobile" class="text-center font-weight-bold font-size-text">
        <br>{{contracts[0].dataStipula | date:'dd/MM/yyyy'}}</div>
    </div>
  </div>
</div>

<div class="modal-div show" *ngIf="showLayoutWithClientId" #modalWindowWithClientId>
  <div class="inner-modal-div">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow(this.modalWithClientId)"></i>
    <img class="modal-image" src="/assets/img/icons/new_ok.png" alt="OK">
    <h1 class="text-center">Ecco il tuo Codice Cliente Optima: {{selectedContract.idCliente}}</h1>
    <div class="modal-text">Segui gli aggiornamenti sull’attivazione delle tue utenze nella sezione “I tuoi servizi”
      della tua Area Clienti.
    </div>
    <button class="btn-modal" (click)="moveToGeneralLoginPage()">ACCEDI</button>
  </div>
</div>

<div class="modal-div show" *ngIf="showLayoutWithCCN" #modalWindowWithCCN>
  <div class="inner-modal-div padding">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow(this.modalWithCCN)"></i>
    <!--Category1-->
    <div *ngIf="selectedContract.categorieCCN === categoryCCN.Category1 && !selectedContract?.isSentDocuments" #sendDocumentsModal>
      <div class="header-upload-modal">
        <img src="assets/img/icons/exclamation-point.png" class="alert-picture" alt="Warning">
        Ci sono dei dati o dei documenti mancanti.
      </div>
      <div class="upload-modal-text">Per procedere con la lavorazione del tuo contratto, abbiamo bisogno di una copia
        chiara e leggibile di: <b>{{selectedContract.documentiDaRecuperare}}.</b>
        <br>
        Il tuo agente Optima di fiducia ti ricontatterà per aiutarti a recuperarlo. Se hai già
        il documento a portata di mano puoi allegarlo direttamente qui.
      </div>
      <div class="upload-file-button" *ngIf="emailFilesBeforeValidation.length < 5">
        <label for="files-upload">
          <img class="upload-button" src="assets/img/icons/upload-file.png" alt="Upload button">
          <input type="file"
                 (change)="onFilesChange($event)"
                 accept="application/pdf"
                 id="files-upload"
                 name="frontDocumentPhoto">
        </label>
      </div>
      <div class="upload-input-block" *ngFor="let file of emailFilesBeforeValidation; let i = index">
        <div class="upload-file-name" *ngIf="!emailIndexesOfFilesWithWrongSize.includes(i)
                  && !emailIndexesOfFilesWithWrongExtension.includes(i)">{{emailFilesBeforeValidation[i].name}}</div>
        <img (click)="removeFileFromForm(i, emailFilesAfterValidation.indexOf(file))" *ngIf="!emailIndexesOfFilesWithWrongSize.includes(i)
                && !emailIndexesOfFilesWithWrongExtension.includes(i)" src="assets/img/icons/delete_icon.png"
             alt="remove" class="remove-file">
        <div *ngIf="emailIndexesOfFilesWithWrongSize.includes(i)" class="text-danger">
          La dimensione del file deve essere inferiore a 5 MB
        </div>
        <div *ngIf="emailIndexesOfFilesWithWrongExtension.includes(i)" class="text-danger">
          L’estensione del file deve essere PDF
        </div>
        <button *ngIf="emailIndexesOfFilesWithWrongSize.includes(i)
                || emailIndexesOfFilesWithWrongExtension.includes(i)"
                class="remove-file-button" (click)="closeWarningInEmailForm(i)">×
        </button>
      </div>
      <button class="button-calling-modal-window font-weight-bold" *ngIf="emailFilesBeforeValidation.length > 0"
              style="margin-top: 10px" (click)="sendFile()">PROSEGUI
      </button>
    </div>
    <div *ngIf="selectedContract?.isSentDocuments || selectedContract?.isBookedCall">
      <div class="header-upload-modal">
        <img src="assets/img/icons/exclamation-point.png" class="alert-picture" alt="Warning">
        La tua richiesta è stata presa in carico
      </div>
      <div class="upload-modal-text">Riceverai informazioni sulla lavorazione del tuo contratto entro le prossime 48 ore
        lavorative. Grazie.
      </div>
    </div>
    <!--Category2-->
    <div [formGroup]="formGroupSupportCall"
         *ngIf="selectedContract.categorieCCN === categoryCCN.Category2 && !selectedContract?.isBookedCall">
      <div class="header-upload-modal">
        <img src="assets/img/icons/exclamation-point.png" class="alert-picture" alt="Warning">
        Ci sono delle informazioni da verificare per procedere con la lavorazione del tuo contratto.
      </div>
      <div class="upload-modal-text">Il tuo agente Optima di fiducia ti contatterà a breve per risolvere la situazione.
        Se
        vuoi puoi indicare il numero di telefono e l’orario in cui preferisci farti chiamare.
      </div>
      <div class="input-block">
        <div class="label-input">Il tuo numero:</div>
        <input class="input-number-block" style="width: 225px" formControlName="phoneNumber"/>
      </div>
      <span class="text-danger show text-center" *ngIf="formGroupSupportCall.get('phoneNumber').hasError('required')
      && (formGroupSupportCall.get('phoneNumber').dirty || formGroupSupportCall.get('phoneNumber').touched)">Campo obbligatorio</span>
      <span class="text-danger show text-center" *ngIf="(formGroupSupportCall.get('phoneNumber').hasError('minLength') ||
      formGroupSupportCall.get('phoneNumber').hasError('maxLength')) && (formGroupSupportCall.get('phoneNumber').dirty
      || formGroupSupportCall.get('phoneNumber').touched)">Il numero di telefono deve essere composto da almeno 8 e non più di 12 cifre
      </span>
      <div class="input-block">
        <div class="label-input">Quando vuoi essere richiamato:</div>
        <select class="input-number-block" formControlName="contactTime">
          <option *ngFor="let item of contactTimeOptions | keys" value="{{item}}">
            {{contactTimeOptions[item]}}
          </option>
        </select>
        <app-info class="contact-time-info">
          <info-button><i class="info-circle">i</i></info-button>
          <info-message>
            <div class="info-title">
              Un nostro esperto proverà a contattarti nella fascia oraria da te indicata appena possibile.
              La chiamata sarà effettuata negli orari di apertura del Servizio Clienti Optima.
            </div>
          </info-message>
        </app-info>
      </div>
      <span class="text-danger show text-center" *ngIf="formGroupSupportCall.get('contactTime').hasError('required')
      && (formGroupSupportCall.get('contactTime').dirty || formGroupSupportCall.get('contactTime').touched)">Selezionare una fascia oraria</span>
      <button class="button-calling-modal-window" (click)="sendEmailToSupportCall()">PRENOTA LA CHIAMATA</button>
    </div>
    <!--Category3-->
    <div *ngIf="selectedContract.categorieCCN === categoryCCN.Category3">
      <div class="header-upload-modal">
        <img src="assets/img/icons/exclamation-point.png" class="alert-picture" alt="Warning">
        OPS! C’è un piccolo dettaglio da sistemare per procedere con la lavorazione del tuo contratto
      </div>
      <div class="upload-modal-text">Il tuo agente Optima di fiducia ti contatterà a breve per risolvere la situazione.
      </div>
    </div>
  </div>
</div>
<!--<div class="modal-div show" *ngIf="showLayoutWithAttivazioneParziale" #modalWindowQCCKO>
  <div class="inner-modal-div padding">
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow(this.modalQCCKO)"></i>
    <div class="modal-container">
      <img src="assets/img/icons/new_error.png" class="modal-image" alt="Error">
      <div class="modal-container-text"
           *ngIf="selectedContract.attivazioneParziale === 1">
        Ti informiamo che non è stato possibile procedere con l’attivazione del
        servizio: {{selectedContract.serviziCestinati}}. La
        procedura di attivazione continuerà per gli altri servizi presenti nel tuo contratto
      </div>
      <div class="modal-container-text"
           *ngIf="selectedContract.attivazioneParziale === 0">
        Ti informiamo che non è stato possibile procedere con l’attivazione del
        servizio: {{selectedContract.serviziCestinati}}
      </div>
    </div>
  </div>
</div>-->
<div class="modal-div show"
     *ngIf="showLayoutWithFlagContrattoCestinato || (selectedContract?.attivazioneParziale && !selectedContract?.azioneQCC)"
     #modalWindowFlagContrattoCestinato>
  <div class="inner-modal-div padding" >
    <i class="fa fa-times" aria-hidden="true" (click)="hideModalWindow(this.modalFlagContrattoCestinato)"></i>
    <div class="modal-container">
      <img src="assets/img/icons/new_error.png" class="modal-image" alt="Error">
      <div class="modal-container-text"
           *ngIf="showLayoutWithFlagContrattoCestinato && selectedContract?.attivazioneParziale === 1">
        Ti informiamo che non è stato possibile procedere con l’attivazione del
        servizio: {{selectServiceName.toUpperCase()}}. La procedura di attivazione continuerà per gli altri servizi
        presenti nel tuo contratto
      </div>
      <div class="modal-container-text"
           *ngIf="showLayoutWithFlagContrattoCestinato && selectedContract?.attivazioneParziale === 0">
        Ti informiamo che non è stato possibile procedere con l’attivazione del
        servizio: {{selectServiceName.toUpperCase()}}
      </div>
    </div>
  </div>
</div>
<app-spinner *ngIf="shouldShowSpinner | async"></app-spinner>
