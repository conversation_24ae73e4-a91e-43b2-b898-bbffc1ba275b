import { Component, OnInit } from '@angular/core';
import { select } from '@angular-redux/store';
import { Observable } from 'rxjs/Observable';

@Component({
  selector: 'app-moduli',
  templateUrl: './moduli.component.html',
  styleUrls: ['./moduli.component.scss']
})
export class ModuliComponent implements OnInit {

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  constructor() { }
  ngOnInit() { }

}
