<div class="container-fluid active-layout">

  <div class="page-title">
    <div class="title-image"></div>
    <div class="title-text">MODULI</div>
  </div>

  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding">

    <div class="bordered-block">
      <div class="col-lg-8 col-md-8 col-sm-12  col-xs-12 no-padding">
        <div class="col-lg-1 col-md-1 col-sm-1  col-xs-2 no-padding app-header">MODULI:</div>
        <div class="col-lg-6 col-md-6 col-sm-11 col-xs-10 no-padding col-padding-left" [formGroup]="formGroup">
          <input formControlName="filter" name="filter" class="form-control"/>
        </div>
      </div>

      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding">

        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 no-padding  col-margin-top">
          <mat-expansion-panel [expanded]="isExpanded" class="document-collapse"
                               *ngFor="let cluster of objectKeys(businesDocuments)">
            <mat-expansion-panel-header [collapsedHeight]="'50px'" [expandedHeight]="'50px'"
                                        class="app-first-expansion-panel">
              <mat-panel-title class="cluster-title">
                {{cluster.toUpperCase()}}
              </mat-panel-title>
            </mat-expansion-panel-header>
            <mat-expansion-panel [expanded]="isExpanded" *ngFor="let service of objectKeys(businesDocuments[cluster])">
              <mat-expansion-panel-header>
                <mat-panel-title class="service-title">
                  {{service}}
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div class="form-document" *ngFor="let form of businesDocuments[cluster][service]">
                <p class="document-link" (click)="download(form.name)">{{form.title}}</p>
              </div>
            </mat-expansion-panel>
          </mat-expansion-panel>
        </div>

        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 no-padding col-padding-btw col-margin-top">
          <mat-expansion-panel [expanded]="isExpanded" class="document-collapse"
                               *ngFor="let cluster of objectKeys(consumerDocuments)">
            <mat-expansion-panel-header [collapsedHeight]="'50px'" [expandedHeight]="'50px'"
                                        class="app-first-expansion-panel">
              <mat-panel-title class="cluster-title">
                {{cluster.toUpperCase()}}
              </mat-panel-title>
            </mat-expansion-panel-header>
            <mat-expansion-panel [expanded]="isExpanded" *ngFor="let service of objectKeys(consumerDocuments[cluster])">
              <mat-expansion-panel-header>
                <mat-panel-title class="service-title">
                  {{service}}
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div class="form-document" *ngFor="let form of consumerDocuments[cluster][service]">
                <p class="document-link" (click)="download(form.name)">{{form.title}}</p>
              </div>
            </mat-expansion-panel>
          </mat-expansion-panel>
        </div>
      </div>
    </div>
  </div>
</div>
