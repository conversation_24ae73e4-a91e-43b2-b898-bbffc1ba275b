import {After<PERSON><PERSON>w<PERSON>hecked, ChangeDetector<PERSON>ef, Component, ElementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {BehaviorSubject} from 'rxjs/BehaviorSubject';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import * as moment from 'moment';

import {
  AggregatedBillingInformation,
  Contabile,
  DilazioneClienteData,
  FattureModel,
  RichiediDilazione,
  ShortBillInformation
} from '../../invoice.model';
import {InvoiceService} from '../../invoice.service';
import {DilazioneActions} from '../../../../redux/dilazione/actions';
import {DialogModalActions} from '../../../../redux/dialogModal/actions';
import {DialogModalEntity} from '../../../../common/model/dialogModal/DialogModalEntity';
import {UserData} from '../../../../common/model/userData.model';
import {RichiediActions} from '../../../../redux/richiedi/actions';
import {OffersService} from '../../../../common/services/offers/offers.service';
import {UserActions} from '../../../../redux/user/actions';
import {getChangeDateScandezaRequest} from '../../../profilePage/config/config';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {Subscription} from 'rxjs/Subscription';
import {ObservableUtils} from '../../../../common/utils/ObservableUtils';
import {Offer} from '../../../../redux/model/Offer';
import {IncidentEventResponse} from '../../../autolettura/model/IncidentEventResponse';
import {
  PagamentoFlessibileService
} from '../../../../common/services/pagamento-flessibile/pagamento-flessibile.service';
import {PayPalService} from '../../../../common/services/paypal/pay-pal-service.service';
import {UserDataService} from '../../../../common/services/user-data/userData.service';
import {FormUtils} from '../../../../common/utils/FormUtils';
import {AttachmentService} from '../../../fai-da-te/services/attachment/attachment.service';


@Component({
  selector: 'app-user-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.scss']
})

export class InvoiceComponent implements OnInit, AfterViewChecked, OnDestroy {

  @ViewChild('paginator') paginatorElement: any;

  @select(['spinner', 'show'])
  shouldShowSpinner: Observable<boolean>;

  @select(['dilazione'])
  dilazione: Observable<DilazioneClienteData>;

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  //
  // @select(['user', 'activeOffers'])
  // clientActiveOffers: Observable<any>;

  formGroupAleggaPagamento: FormGroup;
  emailFilesBeforeValidation: Array<File> = [];
  emailFilesAfterValidation: Array<File> = [];
  emailIndexesOfFilesWithWrongSize = [];
  emailIndexesOfFilesWithWrongExtension = [];
  @ViewChild('filesInput')
  filesInput: ElementRef;

  userData: UserData;

  dilazioneData: DilazioneClienteData;

  richiediDilazioneData: RichiediDilazione;

  pdf: any;

  pagaList: Array<Number> = [];

  selectedSlittamentoProssimaList: Array<Offer> = [];

  allPagaList: Array<Number> = [];

  contabile: Contabile;

  pagaSumm: number;

  esito: number;

  saldo: number;

  saldoInScadenza: number;

  invoices = [];

  paginatorListener: BehaviorSubject<Array<any>>;

  invoiceTableData: Array<any>;

  invoiceData: Array<any>;

  formGroup: FormGroup;

  needToHide: Array<any> = [];

  modalEntity: DialogModalEntity;

  currentYear = moment().year() - 1;
  isMobile = window.innerWidth <= 767;

  shouldShowRichiediButton = true;

  showInfoDialog = false;

  checkIncidentChangeDateScandenza = false;

  checkIncidentEventPagamentoFlessibile = false;

  active: Array<Offer>;

  item: Offer;

  payPalPayment: any;

  fatture: Array<any> = [];

  fattureItem: FattureModel;

  incidentEventSubscription: Subscription;
  showSlittamentoProssimaModal = false;
  showConfermaSlittamentoProssimaModal = false;
  showSuccessConfermaSlittamentoProssimaModal = false;
  showFailConfermaSlittamentoProssimaModal = false;
  showFrequentConfermaSlittamentoProssimaModal = false;
  showPagamentoFlessibileButton = false;
  prosimaFattureSubscription: Subscription;
  showSpinner: boolean;
  showPagamentoModalAllFattura = false;
  showPagamentoModalSelectedFattura = false;
  showAllegaPagamento: boolean;
  showAllegaPagamentoInstruction: boolean;
  showAllegaPagamentoUploadFileModalWindow: boolean;
  showSuccessModalWindow: boolean;
  atLeastOneItemSold = false;
  isDebtSoldToRepaymentCompany = false;
  ceduteSum = 0;
  isUserBusiness: boolean;
  isUserPA: boolean;
  aggregatedBillingInformation: AggregatedBillingInformation[] = [];
  aggregatedBillsToTable: AggregatedBillingInformation[] = [];
  formGroupSearchByKey: FormGroup;
  formGroupYearReport: FormGroup;
  showNotFindFileWindow: boolean;

  constructor(private service: InvoiceService, private formBuilder: FormBuilder, private actions: DilazioneActions,
              private cdRef: ChangeDetectorRef, private offersService: OffersService, private dialogModalActions: DialogModalActions,
              private modalActions: DialogModalActions, private richiediActions: RichiediActions, private userActions: UserActions,
              private incidentEventService: IncidentEventService, private pagamentoFlessibileService: PagamentoFlessibileService,
              private payPalService: PayPalService, private userDataService: UserDataService, private attachmentService: AttachmentService) {
    this.service.getInvoiceData().subscribe(invoices => {
      let counter = 0;
      invoices.forEach(invoice => {
        if (invoice.isSold) {
          counter++;
          this.atLeastOneItemSold = true;
          this.ceduteSum += invoice.amountTransferred;
        }
      });
      if (this.saldo === 0 && this.atLeastOneItemSold) {
        this.isDebtSoldToRepaymentCompany = true;
      } else if (this.saldo > 0 && this.atLeastOneItemSold) {
        this.isDebtSoldToRepaymentCompany = false;
      }
      this.dilazione.subscribe(dilazione => {
        if (Object.keys(dilazione.dilazione).length === 0) {
          this.service.getDilazioneClienteData().subscribe(value => {
            this.actions.setDilazione(value.dilazione, value.esito);
            if (value.dilazione['fatture']) {
              invoices.forEach(inv => {
                value.dilazione['fatture'].forEach(dil => {
                  if (inv.numeroFattura === dil.numeroDocumento) {
                    this.needToHide.push(inv.numeroFattura);
                  }
                });
              });
            }
            this.actions.setDilazione(value.dilazione, value.esito);
            this.dilazioneData = value;
          });
        } else {
          this.service.getDilazioneClienteData().subscribe(value => {
            if (value.dilazione['fatture']) {
              invoices.forEach(inv => {
                value.dilazione['fatture'].forEach(dil => {
                  if (inv.numeroFattura === dil.numeroDocumento) {
                    this.needToHide.push(inv.numeroFattura);
                  }
                });
              });
            }
          });
        }
        this.dilazioneData = dilazione;
      });

      if (invoices) {
        this.invoices = invoices;
        const today = new Date().getTime();
        let pagaSumm = 0;
        invoices.forEach(invoice => {
          if (invoice.total !== invoice.totalEvasion && invoice.endDate < today) {
            if (invoice.opened) {
              pagaSumm += invoice.opened;
            }
            this.allPagaList.push(invoice.id);
          }
        });
        this.pagaSumm = Math.round(pagaSumm * 100) / 100;

        const array = invoices.filter(function (option) {
          return (option.downloadUrl && option.oscurata === false && option.validataSdi === true);
        }).sort((o1, o2) => o2.startDate - o1.startDate);
        this.invoiceTableData = array;
        this.invoiceData = array;
        this.paginatorListener = new BehaviorSubject([]);
        this.formGroup = formBuilder.group({
          invoiceId: [],
          startDate: []
        });
      }
    });
    this.service.getContanibleData().subscribe(contabile => {
      this.contabile = contabile;
    });
    this.service.getRichiediDilazioneData().subscribe(data => {
      this.richiediActions.setRichiedi(data);
      this.richiediDilazioneData = data;
    });
    // this.dilazione.subscribe(dilazione => {
    //   if (Object.keys(dilazione.dilazione).length === 0) {
    //     this.service.getDilazioneClienteData().subscribe(value => {
    //       this.actions.setDilazione(value.dilazione, value.esito);
    //       this.dilazioneData = value;
    //     });
    //   }
    //   this.dilazioneData = dilazione;
    // });
    this.service.getSaldo().subscribe(saldo => {
      this.saldo = saldo;
      this.userDataService.getLatestCreditPolicyStatus(localStorage.getItem('clientId')).subscribe(latestCreditPolicyStatus => {
        if (this.saldo > 0 && (latestCreditPolicyStatus.response === 'Inviata Raccomandata' || latestCreditPolicyStatus.response === 'Inviato Blocco')) {
          this.showAllegaPagamento = true;
        }
      });
    });
    this.service.getSaldoInScadenza().subscribe(saldoInScadenza => {
      this.saldoInScadenza = saldoInScadenza;
    });
    this.userInfo.subscribe(data => {
      this.userData = data;
      this.isUserBusiness = this.userData && this.userData.cluster.value === 'BUSINESS';
      this.isUserPA = this.userData && this.userData.cluster.value === 'Pubblica Amministrazione';
      if (this.isUserPA) {
        this.service.getBillingCenterInformation().subscribe(billingInformation => {
          if (billingInformation.response) {
            const counters = {};
            billingInformation.response.forEach(bill => {
              counters[bill.idFatt] = counters[bill.idFatt] || { pod: 1, pdr: 1 };
              const symbol = bill.tipo === 'ENERGIA' ? `POD ${counters[bill.idFatt].pod++}` : `PDR ${counters[bill.idFatt].pdr++}`;
              const aggregatedBill = this.aggregatedBillingInformation.find(ab => ab.idFatt === bill.idFatt);
              if (aggregatedBill) {
                aggregatedBill.associatedBills.push(new ShortBillInformation(bill, symbol));
              } else {
                this.aggregatedBillingInformation.push(new AggregatedBillingInformation(bill, symbol));
              }
            });
          }
          this.aggregatedBillsToTable = this.aggregatedBillingInformation.sort((a, b) => a.idFatt - b.idFatt);
        });
      }
    });
    this.offersService.loadClientOffers(localStorage.getItem('clientId')).subscribe(
      (response: any) => {
        this.userActions.clientOffersLoaded(response);
          this.active = response.filter(value => value.tipoCommercialeDescrizione === 'Super Impresa' && value.billingId !== 4);
          this.pagamentoFlessibileService.isPagamentoFlessibile().subscribe(item => {
            this.offersService.checkIncidentEventPagamentoFlessibile().subscribe(item1 => {
              this.showPagamentoFlessibileButton = item && item1;
            });
          });
      });
    // if (this.offersService) {
    //   this.offersService.checkIncidentChangeDateScandenza().subscribe(isCloseIncidentChangeDateScandenza => {
    //     this.checkIncidentChangeDateScandenza = isCloseIncidentChangeDateScandenza;
    //   });
    //   this.offersService.checkIncidentEventPagamentoFlessibile().subscribe(isCloseIncidentEventPagamentoFlessibile => {
    //     this.checkIncidentEventPagamentoFlessibile = isCloseIncidentEventPagamentoFlessibile;
    //   });
    // }
    this.formGroupAleggaPagamento = this.formBuilder.group({
      description: [null, Validators.required],
      files: [null, Validators.required]
    });
    this.formGroupSearchByKey = this.formBuilder.group({
      searchValue: [null, Validators.required]
    });
    this.formGroupYearReport = this.formBuilder.group({
      year: [this.currentYear, Validators.required]
    });
  }


  showHideInfoWindow() {
    this.showInfoDialog = !this.showInfoDialog;
  }

  getUserId() {
    return localStorage.getItem('clientId');
  }

  isUserCondominio() {
    return localStorage.getItem('sottotipoCluster') === 'Condominio';
  }

  isDisabledByFatture(val): boolean {
    if (this.needToHide.includes(val)) {
      return true;
    }
  }

  applyFilter() {
    const value = this.formGroup.value;
    let data = this.invoiceData;
    if (value.invoiceId) {
      data = data.filter(item => item.numeroFattura.indexOf(value.invoiceId, 0) === 0);
    }
    if (value.startDate) {
      const startDateTimestamp = moment(value.startDate).valueOf();
      data = data.filter(item =>
        moment(moment(item.startDate).format('MM-DD-YYYY')).valueOf() === startDateTimestamp
      );
    }
    this.invoiceTableData = data;
  }

  searchByValue() {
    const value = this.formGroupSearchByKey.value;
    if (!value.searchValue) {
      this.aggregatedBillsToTable = this.aggregatedBillingInformation;
      return;
    }
    this.aggregatedBillsToTable = this.aggregatedBillingInformation.filter((aggregatedInfo) =>
      aggregatedInfo.associatedBills.some((shortBillInfo) =>
        shortBillInfo.utenza.includes(value.searchValue.toUpperCase())));
  }

  downloadYearlyReport() {
    if (this.formGroupYearReport.value.year) {
      this.service.getExcelFile(this.formGroupYearReport.value.year).subscribe(response => {
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const data = window.URL.createObjectURL(blob);
        const file = document.createElement('a');
        file.download = `RendicontazionePubblicaAmministrazione_${this.formGroupYearReport.value.year}.xlsx`;
        file.href = data;
        document.body.appendChild(file);
        file.click();
        document.body.removeChild(file);
      }, () => {
        this.showNotFindFileWindow = true;
      });
    }
  }

  downloadFile(endUrl) {
    this.service.getPdf(endUrl).subscribe(res => {
      const blob = new Blob([res], {type: 'application/pdf'});
      const data = window.URL.createObjectURL(blob);
      window.open(data, '_blank');
    });
  }

  downloadTrafficoFile(endUrl) {
    this.service.getTrafficoPdf(endUrl).subscribe(res => {
      const blob = new Blob([res], {type: 'application/pdf'});
      const data = window.URL.createObjectURL(blob);
      window.open(data, '_blank');
    });
  }

  handleSlittamentoProssimaChange(value, event) {
    if (event.target.checked) {
      this.selectedSlittamentoProssimaList.push(value);
    } else {
      this.selectedSlittamentoProssimaList.splice(this.selectedSlittamentoProssimaList.indexOf(value), 1);
    }
  }

  handleChange(value, event) {
    if (event.target.checked) {
      this.pagaList.push(value.id);
    } else {
      this.pagaList.splice(this.pagaList.indexOf(value.id), 1);
    }
  }

  isAlreadyMarked(value): boolean {
    return this.pagaList.includes(value.id);
  }

  ngOnInit(): void {
    window.scrollTo(0, 0);
  }

  openAllegaPagamento() {
    this.showAllegaPagamentoInstruction = true;
  }

  openAllegaPagamentUploadFileModalWindow() {
    this.showAllegaPagamentoUploadFileModalWindow = true;
    this.showAllegaPagamentoInstruction = false;
  }

  closeWarningInEmailForm(index: number) {
    this.emailFilesBeforeValidation.splice(index, 1);
    this.emailIndexesOfFilesWithWrongSize = this.emailIndexesOfFilesWithWrongSize.filter(item => item !== index);
    this.emailIndexesOfFilesWithWrongExtension = this.emailIndexesOfFilesWithWrongExtension.filter(item => item !== index);
  }

  removeFileFromEmailForm(beforeValIndex: number, afterValIndex: number) {
    this.emailFilesBeforeValidation.splice(beforeValIndex, 1);
    this.emailFilesAfterValidation.splice(afterValIndex, 1);
    for (let i = 0; i < this.emailIndexesOfFilesWithWrongSize.length; i++) {
      if (this.emailIndexesOfFilesWithWrongSize[i] > beforeValIndex) {
        this.emailIndexesOfFilesWithWrongSize[i]--;
      }
    }
    for (let i = 0; i < this.emailIndexesOfFilesWithWrongExtension.length; i++) {
      if (this.emailIndexesOfFilesWithWrongExtension[i] > beforeValIndex) {
        this.emailIndexesOfFilesWithWrongExtension[i]--;
      }
    }
    this.formGroupAleggaPagamento.get('files').patchValue(this.emailFilesAfterValidation);
  }

  validateFile(name: String) {
    const ext = name.substring(name.lastIndexOf('.') + 1);
    return ext.toLowerCase() === 'pdf' || ext.toLowerCase() === 'jpg' || ext.toLowerCase() === 'jpeg' || ext.toLowerCase() === 'svg';
  }

  onFileChange(event) {
    let currentFile: File;
    if (event.target.files && event.target.files.length
      && this.emailFilesAfterValidation.length < 5) {
      currentFile = event.target.files[0];
      for (let i = 0; i < this.emailFilesAfterValidation.length; i++) {
        if (this.emailFilesAfterValidation[i].name === currentFile.name) {
          event.target.value = '';
          return;
        }
      }
      this.emailFilesBeforeValidation.push(currentFile);
      this.emailFilesAfterValidation.push(currentFile);

      if (currentFile.size > 5242880) {
        this.emailIndexesOfFilesWithWrongSize.push(this.emailFilesBeforeValidation.indexOf(currentFile));
        this.emailFilesAfterValidation.splice(-1, 1);
      } else if (!this.validateFile(currentFile.name)) {
        this.emailIndexesOfFilesWithWrongExtension.push(this.emailFilesBeforeValidation.indexOf(currentFile));
        this.emailFilesAfterValidation.splice(-1, 1);
      }

      const reader = new FileReader();
      if (this.emailFilesAfterValidation.includes(currentFile)) {
        reader.readAsDataURL(currentFile);
      }
      reader.onload = () => {
        this.formGroupAleggaPagamento.get('files').patchValue(this.emailFilesAfterValidation);
      };
    }
    event.target.value = '';
  }

  openIncidentWithAttachment() {
    if (!this.formGroupAleggaPagamento.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroupAleggaPagamento);
    } else {
      this.showAllegaPagamentoUploadFileModalWindow = false;
      const formData = new FormData();
      for (const file of this.formGroupAleggaPagamento.get('files').value) {
        formData.append('files', file);
      }
      formData.append('message', `Allegato pagamento da Area Clienti. Procedere ad incasso da fax. Messaggio del cliente: ${this.formGroupAleggaPagamento.value.description}`);
      this.incidentEventService.openIncidentEventForAllegaPagamento().subscribe((data: IncidentEventResponse) => {
        if (data && data.status === 'OK') {
          formData.append('incidentId', data.incidentId);
          this.attachmentService.sendMultiAttachment(formData).subscribe();
          this.showSuccessModalWindow = true;
        }
      });
      this.filesInput.nativeElement.value = '';
      this.formGroupAleggaPagamento.reset();
      this.resetFilesVariablesForEmail();
    }
  }

  resetFilesVariablesForEmail() {
    this.emailFilesBeforeValidation = [];
    this.emailFilesAfterValidation = [];
    this.emailIndexesOfFilesWithWrongExtension = [];
    this.emailIndexesOfFilesWithWrongSize = [];
  }

  openPaymentForAllFattura(pagaList) {
    if (pagaList && pagaList.length) {
      this.showPagamentoModalAllFattura = true;
    }
  }

  openPaymentForAllFatturaByCreditCart(pagaList) {
    window.open(`/api/properties/payment?invoices=${pagaList}&clientId=${localStorage.getItem('clientId')}&access_token=${localStorage.getItem('access_token')}`, '_blank');
    this.showPagamentoModalAllFattura = false;
  }

  openPaymentForAllFatturaByPayPal(pagaList) {
    this.fatture = new Array<any>();
    this.paginatorListener.subscribe(item => {
      for (let i = 0; i < item.length; i++) {
        for (let j = 0; j < pagaList.length; j++) {
          if (item[i].id === pagaList[j]) {
            this.fattureItem = new FattureModel();
            this.fattureItem.IdInvoice = item[i].id;
            this.fattureItem.Importo = item[i].opened.toFixed(2);
            this.fattureItem.DataDocumento = moment(item[i].startDate).format('DD/MM/YYYY');
            this.fattureItem.NumeroDocumento = item[i].numeroFattura;
            this.fatture.push(this.fattureItem);
          }
        }
      }
    });
    this.payPalPayment = {
      'TipoPagamento': 'Paypal',
      'SistemaChiamante': 'Selfcare',
      'Data': moment(Date.now()).format('DD/MM/YYYY'),
      'TotalePagamento': this.saldo.toFixed(2),
      'Fatture': this.fatture,
      'CodiceCliente': this.userData.id,
      'RagioneSociale': this.userData.nameInInvoice,
      'CF': this.userData.fiscalCode,
      'PIVA': this.userData.vatNumber
    };
    this.payPalService.postPayPalActivation(this.payPalPayment).subscribe(item =>
      window.open(item.Return_URL));
    this.showPagamentoModalAllFattura = false;
  }

  openPaymentForSelectedFattura(pagaList) {
    if (pagaList && pagaList.length) {
      this.showPagamentoModalSelectedFattura = true;
    }
  }

  openPaymentForSelectedFatturaByCreditCart(pagaList) {
    window.open(`/api/properties/payment?invoices=${pagaList}&clientId=${localStorage.getItem('clientId')}&access_token=${localStorage.getItem('access_token')}`, '_blank');
    this.showPagamentoModalSelectedFattura = false;
  }

  openPaymentForSelectedFatturaByPayPal(pagaList) {
    this.fatture = new Array<any>();
    let totalValue = 0;
    this.paginatorListener.subscribe(item => {
      for (let i = 0; i < item.length; i++) {
        for (let j = 0; j < pagaList.length; j++) {
          if (item[i].id === pagaList[j]) {
            this.fattureItem = new FattureModel();
            this.fattureItem.IdInvoice = item[i].id;
            this.fattureItem.Importo = item[i].opened.toFixed(2);
            this.fattureItem.DataDocumento = moment(item[i].startDate).format('DD/MM/YYYY');
            this.fattureItem.NumeroDocumento = item[i].numeroFattura;
            totalValue += +this.fattureItem.Importo;
            this.fatture.push(this.fattureItem);
          }
        }
      }
    });
    this.payPalPayment = {
      'TipoPagamento': 'Paypal',
      'SistemaChiamante': 'Selfcare',
      'Data': moment(Date.now()).format('DD/MM/YYYY'),
      'TotalePagamento': totalValue.toFixed(2),
      'Fatture': this.fatture,
      'CodiceCliente': this.userData.id,
      'RagioneSociale': this.userData.nameInInvoice,
      'CF': this.userData.fiscalCode,
      'PIVA': this.userData.vatNumber
    };
    this.payPalService.postPayPalActivation(this.payPalPayment).subscribe(item =>
      window.open(item.Return_URL, '_blank'));
    this.showPagamentoModalSelectedFattura = false;
  }

  getFatturaButtonStyle() {
    return {
      'color': this.pagaList.length === 0 ? '#9BC641' : 'white',
      'border': '2px solid #9BC641',
      'cursor': this.pagaList.length === 0 ? 'auto' : 'pointer',
      'background': this.pagaList.length === 0 ? '' : '#9BC641'
    };
  }

  getSlittamentoProssimaFatturaButtonStyle() {
    return {
      'color': this.selectedSlittamentoProssimaList.length === 0 ? '#9BC641' : 'white',
      'border': '2px solid #9BC641',
      'cursor': this.selectedSlittamentoProssimaList.length === 0 ? 'auto' : 'pointer',
      'background': this.selectedSlittamentoProssimaList.length === 0 ? '' : '#9BC641'
    };
  }

  showRichiediDilazioneModal() {
    this.modalActions.showDialogModal(this.buildDialogModalEntity());
  }


  setScadenzaScadutoValues(dataSetting: Array<object>) {
    if (this.saldo > 0) {
      dataSetting.unshift({
          labelText: 'Importo scaduto',
          labelType: 'text',
          formName: 'scaduto',
          value: this.saldo.toFixed(2),
          rowStyle: {'background': '#ffffff'}
        }
      );
    }
    if (this.saldoInScadenza > 0) {
      dataSetting.unshift({
        labelText: 'Importo in scadenza',
        labelType: 'text',
        formName: 'scadenza',
        value: this.saldoInScadenza.toFixed(2),
        rowStyle: this.saldoInScadenza === 0 ? {'background': '#ffffff'} : {'background': '#f0f5f9'}
      });
    }
  }

  buildScadenzaResult(): Object {
    if (this.saldoInScadenza !== 0 && this.saldo !== 0) {
      let dropdownValues = [];
      if (this.richiediDilazioneData.importoDilazionabileSoloScaduto < 50) {
        dropdownValues = [{
          value: 'Richiesta Dilazione Importo Scaduto e in scadenza (Tot. Esposizione)',
          text: 'Richiesta Dilazione Importo Scaduto e in scadenza (Tot. Esposizione)'
        },
          {value: '', text: ''}
        ];
      } else {
        dropdownValues = [
          {value: 'Richiesta Dilazione Importo scaduto', text: 'Richiesta Dilazione Importo scaduto'},
          {
            value: 'Richiesta Dilazione Importo Scaduto e in scadenza (Tot. Esposizione)',
            text: 'Richiesta Dilazione Importo Scaduto e in scadenza (Tot. Esposizione)'
          },
          {value: '', text: ''}
        ];
      }
      return {
        labelText: 'Importo dilazione',
        labelType: 'dropdown',
        formName: 'importoDilazione',
        placeholder: 'Inserisci importo dilazione',
        needValidation: 'dropdown',
        hasError: false,
        dropdownValues: dropdownValues,
      };
    }

    if (this.saldoInScadenza === 0) {
      return {
        labelText: 'Importo dilazione',
        labelType: 'text',
        formName: 'importoDilazione',
        value: 'Importo scaduto',
        rowStyle: {'background': '#ffffff'}
      };
    }

    if (this.saldo === 0) {
      return {
        labelText: 'Importo dilazione',
        labelType: 'text',
        formName: 'importoDilazione',
        value: 'Importo in scadenza',
        rowStyle: {'background': '#ffffff'}
      };
    }
  }

  buildDataFormSetting(): Array<any> {
    const cadenzeList = [];
    this.richiediDilazioneData.cadenzeList.forEach((a) => {
      if (a.descrizione !== 'giorno fisso') {
        cadenzeList.push({
          value: a.descrizione,
          text: a.descrizione
        });
      }
    });
    cadenzeList.push({value: '', text: ''});

    const modalitaPagamentoList = [];
    const hasModalitaPagamentoOptions = this.richiediDilazioneData.modalitaPagamentoList &&
                                       this.richiediDilazioneData.modalitaPagamentoList.length > 0;
    let defaultModalitaPagamento = '';

    if (hasModalitaPagamentoOptions) {
      this.richiediDilazioneData.modalitaPagamentoList.forEach((modalita) => {
        const descrizione = modalita.descrizione === 'SDD' ? 'Addebito diretto (SDD)' : modalita.descrizione;
        modalitaPagamentoList.push({
          value: modalita.codice,
          text: descrizione
        });
        if (modalita.descrizione === 'SDD') {
          defaultModalitaPagamento = modalita.codice;
        }
      });
    }

    const dataSetting = [
      {
        labelText: 'Causale',
        labelType: 'dropdown',
        formName: 'casuale',
        placeholder: 'Inserisci il motivo della richiesta',
        dropdownValues: [
          {value: 'Problemi di liquidità', text: 'Difficoltà finanziarie'},
          {value: 'Presenza CMOR', text: 'Presenza CMOR in Fattura'},
          {value: 'Calamità naturale', text: 'Calamità naturale'},
          {value: 'Presenza Conguagli a sfavore', text: 'Importo fattura maggiore del solito'},
          // {value: 'Saldo CR negativo', text: 'Saldo CR negativo'},
          {value: 'Ricostruzione consumi', text: 'Ricostruzione consumi'},
          {value: '', text: ''},
        ],
        rowStyle: {'background': '#f0f5f9'},
        needValidation: 'dropdown',
        hasError: false
      },
      this.buildScadenzaResult(),
      hasModalitaPagamentoOptions ? {
        labelText: 'Modalità Pagamento',
        labelType: 'dropdown',
        formName: 'modalitaPagamento',
        placeholder: 'Seleziona modalità di pagamento',
        dropdownValues: modalitaPagamentoList,
        defaultValue: defaultModalitaPagamento, // Додаємо значення за замовчуванням
        rowStyle: {'background': '#ffffff'},
        needValidation: 'dropdown',
        hasError: false
      } : {
        labelText: 'Modalità Pagamento',
        labelType: 'text',
        formName: 'modalitaPagamento',
        value: 'Non ci sono opzioni di pagamento rateale',
        rowStyle: {'background': '#ffffff'},
        editable: false
      },
      {
        labelText: 'Numero rate',
        labelType: 'text',
        editable: true,
        formName: 'numeroRate',
        rowStyle: {'background': '#f0f5f9'},
        needValidation: 'maxNumber',
        hasError: false,
        placeholder: 'Inserisci numero rate'
      },
      {
        labelText: 'Cadenza rate',
        placeholder: 'inserisci cadenza rate',
        labelType: 'dropdown',
        formName: 'scadenzaRate',
        dropdownValues: cadenzeList,
        defaultValue: '30 giorni',
        needValidation: 'dropdown',
        hasError: false
      },
      {
        labelText: 'Indirizzo e-mail',
        labelType: 'text',
        value: this.userData.email,
        editable: true,
        placeholder: 'Inserisci e-mail',
        formName: 'email',
        rowStyle: {'background': '#f0f5f9'},
        needValidation: 'text',
        hasError: false
      },
      {
        labelText: 'Numero di telefono',
        labelType: 'text',
        value: this.userData.phoneNumber,
        editable: true,
        formName: 'phoneNumber',
        placeholder: 'Inserisci numero di telefono',
        needValidation: 'text',
        hasError: false
      },
      {
        labelText: 'Includi il canone Rai nella dilazione',
        labelType: 'dropdown',
        formName: 'canoneRai',
        dropdownValues: [
          {
            value: 'false', text: 'No'
          },
          {
            value: 'true', text: 'Si'
          }],
        rowStyle: {'background': '#f0f5f9'},
        needValidation: 'dropdown',
        hasError: false
      }
    ];

    this.setScadenzaScadutoValues(dataSetting);

    return dataSetting;
  }

  buildDialogModalEntity(): DialogModalEntity {
    if (this.richiediDilazioneData.fattibilita === true && this.richiediDilazioneData.importoDilazionabileSoloScaduto === 0) {
      return {
        text: 'La tua fattura non è ancora scaduta, per richiedere informazioni sulla dilazione ti invitiamo a contattare il Servizio Clienti Optima.'
      };
    }
    return {
      title: `Richiedi Dilazione`,
      customTitle: true,
      hasDataForm: true,
      dataFormSetting: this.buildDataFormSetting(),
      hasButtons: true,
      callBackOnSubmitFirstModal: () => this.shouldShowRichiediButton = false
    } as DialogModalEntity;
  }

  ngAfterViewChecked() {
    this.cdRef.detectChanges();
  }

  hideDialogModal() {
    this.showSlittamentoProssimaModal = false;
    this.showConfermaSlittamentoProssimaModal = false;
    this.showSuccessConfermaSlittamentoProssimaModal = false;
    this.showFailConfermaSlittamentoProssimaModal = false;
    this.showFrequentConfermaSlittamentoProssimaModal = false;
    this.showPagamentoModalAllFattura = false;
    this.showPagamentoModalSelectedFattura = false;
    this.selectedSlittamentoProssimaList = [];
    this.showAllegaPagamentoInstruction = false;
    this.showAllegaPagamentoUploadFileModalWindow = false;
    this.showSuccessModalWindow = false;
    this.showNotFindFileWindow = false;
  }

  conditionShowOfferByIdFattRichiediSlittamentoProssima(idFatt: number) {
    return this.invoices.filter(invoice => invoice.idFatt === idFatt).length >= 3;
  }

  conditionSDDShowOfferRichiediSlittamentoProssima(idFatt: number): boolean {
    return this.invoices.filter(invoice => invoice.idFatt === idFatt).slice(this.invoices.length - 4).every(invoice =>
      invoice.paymentMode === 'SDD (SEPA DIRECT DEBIT)'
    );
  }


  showRichiediSlittamentoProssimaFatturaModal() {
    if (this.active.length === 1) {
      this.selectedSlittamentoProssimaList.push(this.active[0]);
      this.showConfermaSlittamentoProssimaModal = true;
    } else {
      this.showSlittamentoProssimaModal = true;
    }
  }

  confirmSlittamentoProssimaModal(event: any) {
    event.target.disabled = true;
    this.showConfermaSlittamentoProssimaModal = false;
    this.showSpinner = true;
    this.showSlittamentoProssimaModal = false;
    this.selectedSlittamentoProssimaList.forEach(item => {
      if (this.selectedSlittamentoProssimaList.length < 3) {
        this.incidentEventSubscription = this.incidentEventService.pagamentoFatturaFlessibile(getChangeDateScandezaRequest(
          item.billingId))
          .subscribe((incidentEventResponse: IncidentEventResponse) => {
            if (incidentEventResponse.status === 'OK') {
              this.showSpinner = false;
              this.showSuccessConfermaSlittamentoProssimaModal = true;
            }
          });
      } else if (this.conditionShowOfferByIdFattRichiediSlittamentoProssima(item.billingId) === false) {
        this.showSpinner = false;
        this.showFailConfermaSlittamentoProssimaModal = true;
      } else if (this.conditionSDDShowOfferRichiediSlittamentoProssima(item.billingId)) {
        this.showSpinner = false;
        this.showFrequentConfermaSlittamentoProssimaModal = true;
      }
    });
    this.selectedSlittamentoProssimaList = [];
    this.showPagamentoFlessibileButton = false;
  }

  // confirmSlittamentoProssimaModal() {
  //   const clientId = localStorage.getItem('clientId');
  //   this.selectedSlittamentoProssimaList.forEach(item => {
  //     if (this.selectedSlittamentoProssimaList.length < 3) {
  //       this.incidentEventSubscription = this.incidentEventService.pagamentoFatturaFlessibile(getChangeDateScandezaRequest(
  //         clientId, item.billingId, item.scadenzaAnnoContrattuale))
  //         .subscribe((incidentEventResponse: IncidentEventResponse) => {
  //           if (incidentEventResponse.status === 'OK') {
  //             this.dialogModalActions.showDialogModal(successModalAfterDateScandezaChange);
  //           } else {
  //             this.dialogModalActions.showDialogModal(infoModalAfterDateScandezaChange);
  //           }
  //         });
  //     } else {
  //       this.dialogModalActions.showDialogModal(infoModalAboutUnsavedDateScandezaChangesModalData);
  //     }
  //   });
  // }

  confirmModalSubmit(event: any) {
    this.showConfermaSlittamentoProssimaModal = true;
    this.showSlittamentoProssimaModal = false;
    event.target.disabled = true;
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.prosimaFattureSubscription, this.incidentEventSubscription]);
  }
}
