@import "~app/shared/styles/colors";

.container-fluid {
  margin-top: 7%;
}

.buttons-group {
  padding: 20px 10px;
}

.main {
  border: 1px solid $menu-border;
  border-radius: 10px;
  background-color: $menu-background;
}

.top {
  background-color: white;
  border-radius: 5px 5px 0 0;
  border-bottom: 1px solid $menu-border;
  padding: 15px;
  text-transform: uppercase;
  font-size: 1.6em;
  color: $dark-blue;
  font-weight: bold;
  text-align: center;
}

.buttons-group {
  .link {
    text-transform: uppercase;
    border: 2px solid $menu-border;
    border-radius: 10px;
    margin-top: 10px;
    text-align: left;
    background-color: white;
    font-size: 18px;
    padding: 5px 20px 5px 5px;
    color: $dark-blue;
    cursor: pointer;
    font-weight: bold;
    width: 100%;
  }

  @mixin order($order) {
    .order-#{$order} {
      -webkit-box-ordinal-group: $order;
      -moz-box-ordinal-group: $order;
      -ms-flex-order: $order;
      -webkit-order: $order;
      order: $order;
    }
  }

  .profile-mobile-layout {
    float: left;
    width: 100%;
    margin-top: 2%;
    margin-bottom: 3%;
  }

  .answer-mobile-layout {
    float: left;
    width: 100%;
    margin-top: 2%;
    margin-bottom: 3%;
  }

  @include order(10);
  @include order(11);
  @include order(20);
  @include order(21);
  @include order(30);
  @include order(31);
  @include order(40);
  @include order(41);
  @include order(50);
  @include order(51);
  @include order(60);
  @include order(61);
  @include order(70);
  @include order(71);
  @include order(80);
  @include order(81);
  @include order(90);
  @include order(91);


  .active {
    border-color: $dark-blue;
    background-color: $dark-blue;
    color: white !important;
  }

  .arrow {
    font-size: 26px;
  }

  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  /* optional */
  -webkit-box-align: start;
  -moz-box-align: start;
  -ms-flex-align: start;
  -webkit-align-items: flex-start;
  align-items: flex-start;
}

.text {
  padding-left: 15px;
}


@media only screen and (max-width: 1400px) {
  .buttons-group {
    .link {
      padding: 5px 10px 5px 5px;
    }

    .text {
      padding-left: 10px;
    }
  }

}

@media only screen and (max-width: 1300px) {
  .buttons-group {
    .text {
      font-size: 16px;
    }

    .arrow {
      font-size: 24px;
    }
  }
  .top {
    font-size: 18px;
  }
}

@media only screen and (max-width: 1200px) {
  .buttons-group {
    .text {
      font-size: 14px;
    }

    .arrow {
      font-size: 22px;
    }
  }
  .top {
    font-size: 18px;
  }
}

@media only screen and (max-width: 1100px) {
  .buttons-group {
    .text {
      padding: 0;
      font-size: 12px;
    }

    .arrow {
      font-size: 20px;
    }
  }
  .top {
    font-size: 18px;
  }
}

@media only screen and (max-width: 991px) {
  .container-fluid, .profile-layout, .answer-layout {
    padding: 0;
  }
  .buttons-group {
    .profile-mobile-layout {
      display: block;
    }

    .answer-mobile-layout {
      display: block;
    }

    .link {
      padding: 5px 20px 5px 15px;
    }

    .text {
      font-size: 18px;
    }

    .arrow {
      font-size: 26px;
      transform: rotate(90deg);
    }
  }
  .top {
    font-size: 18px;
  }
}
