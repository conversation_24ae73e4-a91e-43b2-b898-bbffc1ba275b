.modal-div {
  width: 100%;
  position: fixed;
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  overflow-y: scroll;
  display: none;
}

.display {
  display: block;
}

.inner-modal-div {
  min-height: 300px;
  margin: auto;
  top: 20vh;
  background: white;
  border-radius: 20px;
  border: 2px solid #36749d;
  position: relative;
  padding-bottom: 30px;
  padding-top: 30px;
  width: 600px;
}

.fa-times {
  position: absolute;
  right: 30px;
  font-size: 30px;
  top: 25px;
  color: #36749d;
  cursor: pointer;
}


@media only screen and (max-width: 768px) {
  .inner-modal-div {
    width: 465px;
  }
}

@media only screen and (max-width: 535px) {
  .inner-modal-div {
    width: 320px;
    height: auto;
    top: 10vh;
  }
  .fa-times {
    font-size: 20px;
  }
}
