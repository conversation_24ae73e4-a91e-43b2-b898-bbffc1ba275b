<div class="content">
  <div class="row main">
    <div class="row top"
         *ngIf="(payData && !(userData | async)?.clienteConvertibile) && (hasActiveOffers | async) && (hasTutoInUnoActive | async)">
      <div class="col-md-1 matryoshka">
      </div>
      <div class="col-md-4 priceBlock">
        <div *ngIf="!isOfferTUS">
          <p class="service">Tutto-In-Uno</p>
          <p class="price">€ {{payData.tuttoInUnoForMensile ? payData.tuttoInUnoForMensile : '0.00'}}</p>
        </div>
        <div *ngIf="isOfferTUS">
          <p class="service">Canone Totale</p>
          <p class="price">€ {{payData.tuttoInUnoForTUS ? payData.tuttoInUnoForTUS : '0.00'}}</p>
        </div>
      </div>
      <div class="col-md-4 priceBlock">

        <p class="service" *ngIf="!isOfferTUS">Conto Relax</p>
        <p class="service" *ngIf="isOfferTUS">Conto Stabilità</p>
        <p class="price">€ {{ payData.contoRelax}}</p>

      </div>
      <i class="i-button" (click)="openModal('Info Saldo','Ci sono periodi in cui è fisiologico consumare di più\n'+
'    (per effetto delle stagioni o delle abitudini)\n'+
'    e avere un saldo di conto negativo.\n'+
'    Con l\'arrivo di periodi a basso consumo il conto si ripianerà.\n'+
'    Se non dovesse ripianarsi sarà nostra premura\n'+
'    contattarti per trovare la soluzione a te più idonea.')">i</i>

    </div>

    <div class="row top pointer" *ngIf="!payData && (userData | async)?.clienteConvertibile"
         [routerLink]="'/passa-tutto-in-uno'">
      <div class="col-md-1 matryoshka">
      </div>
      <div class="col-md-10 passa-tuto-title">
        PASSA A TUTTO-IN-UNO
      </div>
    </div>
    <ul class="ul-mar">
      <li *ngFor='let item of serviceMenu'  routerLinkActive="{{item.active ? 'active' : ''}}" class="{{item.active ? 'able':''}}">
        <div *ngIf="item.title !== 'AMAZON'">
          <a class='link' *ngIf='item.active' mat-ripple [routerLink]="item.link"><span
            class='text'>{{item.title}} </span>
            <i class="fa fa-angle-right" aria-hidden="true"></i></a>
          <div class='disableText' *ngIf='!item.active'>
            <span class="text">{{item.title}}</span>
            <a class='button' *ngIf="item.isScolitoPresent"
               [routerLinkActive]="['active-sciolto']"
               [routerLink]="['/home/<USER>', item.title.toLowerCase()]">
              Scopri di più</a>
<!--            <a *ngIf="item.title==='MOBILE'" class='button' target="_blank"
               href='http://www.optimaitalia.com/mobile.html'>
              Scopri di più</a>-->
          </div>
        </div>
      </li>
      <li class="able " routerLinkActive="active" *ngIf="showServiziButton">
        <div class="amazon-prime-link">
          <a class="link" mat-ripple routerLink='/home/<USER>'><span class='text'>SERVIZI AGGIUNTIVI</span>
            <i class="fa fa-angle-right" aria-hidden="true"></i></a>
        </div>
      </li>
      <li *ngIf="isActiveTeleconsultoMedico">
        <div class="amazon-prime-link">
          <a class="disableText" mat-ripple routerLink='/home/<USER>/teleconsulto-medico'><span class='text'>TELECONSULTO MEDICO</span>
            <a class='button'
               [routerLinkActive]="['active-sciolto']"
               routerLink='/home/<USER>/teleconsulto-medico'>
              Scopri di più</a></a>
        </div>
      </li>
    </ul>
  </div>
  <button *ngIf="isUserCondominio()" class="btn-condomini" (click)="navigateToCondominioPage()">
    <img class="btn-condomini-img" src="assets/img/icons/arrow-down_16x16.png" alt="arrow"/>
    I tuoi Condomini
  </button>
  <div class="row recontact-button-block"
       *ngIf="(userData | async)?.cluster && ((userData | async)?.cluster.value === 'BUSINESS' || (userData | async)?.cluster.value === 'CONSUMER')">
    <!-- &lt;!&ndash;*ngIf="isDashListUserExist"&ndash;&gt; -->
    <div class="recontact-button-desktop" [routerLink]="['/faidate/recontact']">
    </div>
  </div>
  <div class="row banner-5g-block" *ngIf="offers5G.length > 0">
    <div class="banner-5g" [routerLink]="['/faidate/servizi-attivi/mobile/5G', offers5G[0].msisdnId]"></div>
  </div>
<!--  <div class="communication">-->
<!--    <div class="icon"></div>-->
<!--    <div class="title">-->
<!--      Comunicazioni per te-->
<!--    </div>-->
<!--    <ul class="items" *ngIf="hasCommunication | async; else noCommunicationMessage">&lt;!&ndash;&ndash;&gt;-->
<!--      <div *ngFor="let item of data; let i = index">-->
<!--        <li *ngIf="i > data.length - 4" class="item">{{item.subject}}        {{item.creationDate| date : "dd/MM/y"}}</li>-->
<!--      </div>-->
<!--    </ul>-->
<!--    <ng-template #noCommunicationMessage>-->
<!--      <div class="no-communication-message">L'Area Clienti potrebbe non avere tutte le funzionalità normalmente disponibili.-->
<!--        Stiamo rapidamente procedendo al ripristino totale con interventi di perfezionamento e manutenzione.-->
<!--        Ci scusiamo per il temporaneo disagio.</div>-->
<!--    </ng-template>-->
<!--  </div>-->

</div>
