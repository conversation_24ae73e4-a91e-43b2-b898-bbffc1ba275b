<div [formGroup]="group" class="app-form-input clearfix">
  <input [formControlName]="name" class="editor active" [placeholder]="placeholder"/>
  <div class="text-danger errors-block"
       *ngIf="formControl.hasError('error') && (formControl.dirty || formControl.touched)">
    {{formControl.errors['error']}}
  </div>
  <div class="text-danger errors-block"
       *ngIf="formControl.hasError('required') && (formControl.dirty || formControl.touched)">Campo obbligatorio.
  </div>
  <div class="text-danger errors-block" *ngIf="formControl.hasError('minlength') && (formControl.dirty ||
      formControl.touched)">Lunghezza minima {{formControl.errors['minlength'].requiredLength}} caratteri.
  </div>
  <div class="text-danger errors-block"
       *ngIf="formControl.hasError('maxlength') && (formControl.dirty ||
      formControl.touched)">Lunghezza massima {{formControl.errors['maxlength'].requiredLength}} caratteri.
  </div>
  <div class="text-danger errors-block"
       *ngIf="formControl.hasError('pattern') && (formControl.dirty || formControl.touched)">
    Il valore inserito non è valido.
  </div>
  <div class="text-danger errors-block"
       *ngIf="formControl.hasError('validationError') && (formControl.dirty || formControl.touched)">
    {{formControl.errors['validationError']}}
  </div>
</div>
