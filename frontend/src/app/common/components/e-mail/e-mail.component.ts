import { Component, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { FormUtils } from '../../utils/FormUtils';
import { EmailService } from '../../services/email/email.service';
import EmailMessage from '../../model/EmailMessage';

@Component({
  selector: 'app-e-mail',
  templateUrl: './e-mail.component.html',
  styleUrls: ['./e-mail.component.scss']
})
export class EMailComponent implements OnInit {


  @ViewChild('classicModal') classicModal: any;
  formGroup: FormGroup;

  constructor(private formBuilder: FormBuilder, private emailService: EmailService) {
    this.formGroup = this.formBuilder.group({
      'message': ['', [Validators.required, Validators.minLength(5)]],
      'subject': ['', [Validators.required, Validators.minLength(2)]]
    });

  }

  ngOnInit() {
  }

  sendNewEmailMessage() {
    if (!this.formGroup.valid) {
      FormUtils.setFormControlsAsTouched(this.formGroup);
    } else {
      this.formGroup.setValue({
        subject: this.formGroup.get('subject').value + ' ' + ' - Segnalazione da Area Clienti ricevuta dal cliente : '
          + localStorage.getItem('clientId'),
        message:  this.formGroup.get('message').value
      });
      this.emailService.sendEmail(this.formGroup.value as EmailMessage).subscribe(() => {
        this.closeDialog();
      });
    }
  }

  closeDialog() {
    this.formGroup.reset();
    this.classicModal.hide();
  }

}
