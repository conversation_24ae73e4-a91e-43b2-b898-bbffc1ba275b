import {Component, Input, OnInit} from '@angular/core';

@Component({
  selector: 'app-error-window',
  templateUrl: './error-window.component.html',
  styleUrls: ['./error-window.component.scss']
})
export class ErrorWindowComponent implements OnInit {

  @Input() onClose: () => void;

  constructor() { }

  ngOnInit() {
  }

  closeModal() {
    if (this.onClose) {
      this.onClose();  // Execute the callback function
    }
  }

}
