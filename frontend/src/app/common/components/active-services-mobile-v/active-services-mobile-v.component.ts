import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs/Subscription';
import {UserServicesService} from '../../../routes/profilePage/userServices/userServices.service';
import {MenuService} from '../../../core/menu/menu.service';
import {Router} from '@angular/router';
import {ServiceStatus} from '../../enum/ServiceStatus';
import {MobileRecordStatus} from '../../enum/MobileRecordStatus';
import {Observable} from 'rxjs/Observable';
import {select} from '@angular-redux/store';
import {UserData} from '../../model/userData.model';
import {HomeService} from '../../../routes/home/<USER>/home/<USER>';
import {ObservableUtils} from '../../utils/ObservableUtils';
import {DialogModalActions} from '../../../redux/dialogModal/actions';
import {ScioltoService} from '../../services/sciolto/sciolto.service';

@Component({
  selector: 'app-active-services-mobile-v',
  templateUrl: './active-services-mobile-v.component.html',
  styleUrls: ['./active-services-mobile-v.component.scss']
})

export class ActiveServicesMobileComponent implements OnInit, OnDestroy {

  @select(['services', 'services'])
  services: Observable<Array<any>>;

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;

  @select(['user', 'clientOffers'])
  private clientOffers;

  @select(['user', 'hasActiveOffers'])
  hasActiveOffers: Observable<boolean>;

  hasTutoInUnoActive: Observable<boolean>;

  userData: UserData;
  serviceMenu: Array<any>;
  data: Array<any>;
  activeServices: Object;
  allServices: Array<any>;
  url: string;
  submenu: Object;
  userCluster: string;
  pdf: {};
  pdfActive: {};
  payData: object;
  showedDetails = false;
  activeName: string;
  alternativeName = {
    LUCE: ['ENERGIA', 'ELETTRICITA'],
    GAS: ['GAS'],
    FISSO: ['WLR', 'VOCE', 'VOIP'],
    FIBRA: ['ADSL', 'ADSL\HDSL'],
    MOBILE: ['MOBILE']
  };
  showServiziButton: boolean;
  isActiveTeleconsultoMedico: boolean;
  servicesSubscription: Subscription;
  userDataSubscription: Subscription;
  clientOffersSubscription: Subscription;

  constructor(public menu: MenuService, _userService: UserServicesService, private _router: Router,
              private dialogModalActions: DialogModalActions, private router: Router,
              private homeServices: HomeService, private scioltoService: ScioltoService) {
    this.pdf = {};
    this.serviceMenu = menu.getServiceMenu();
    let scolitoInformation;
    let generalScolitoInformation;
    this.userDataSubscription = this.userInfo.subscribe(userInfo => {
      this.userData = userInfo;
      if (userInfo && userInfo.cluster) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.servicesSubscription = this.services.subscribe(services => {
      this.allServices = services;
      this.activeServices = _userService.getActiveServices(this.allServices);
      this.showServiziButton = this.activeServices['AMAZON'] || this.activeServices['Teleconsulto medico']
        || this.activeServices['Assistenza H24'] || this.activeServices['TutelaLegale'];
      this.serviceMenu.forEach(item => {
        item.array = this.getUtility(item.text);
      });
    });
    this.setActiveLink();
    this.scioltoService.getGeneralScioltoInformation().subscribe(generalInformation => {
      generalScolitoInformation = generalInformation.response[0] ? generalInformation.response[0].fields.Offerta : null;
      if (generalScolitoInformation) {
        this.scioltoService.getScioltoInformation(generalScolitoInformation, this.userCluster).subscribe(information => {
          scolitoInformation = information.response[information.response.length - 1];
          this.setInactiveLink(scolitoInformation);
        });
      }
    });
    this.url = this._router.url;
    this.submenu = {};
    this.pdf = {};
    this.pdfActive = {};

  }

  setActiveLink() {
    Object.keys(this.activeServices).map(key => {
      this.getIndexOfService(this.alternativeName[key]);
    });
  }

  getIndexOfService(name) {
    this.serviceMenu.forEach(index => {
      if (index.text === name) {
        index.active = true;
      }
    });
  }

  setInactiveLink(information) {
    const serviceMappings = {
      'LUCE': 'serviziEnergia', 'GAS': 'serviziGas',
      'INTERNET': 'serviziAdsl', 'FISSO': 'serviziVoce', 'MOBILE': 'serviziMobile'
    };
    if (information) {
      this.isActiveTeleconsultoMedico = !this.activeServices['Teleconsulto medico'] && information.serviziVAS.length > 0
        && information.serviziVAS[0].tipoVAS.descrizione === 'Teleconsulto medico';
      this.serviceMenu.forEach(index => {
        const propertyName = serviceMappings[index.text];
        if (propertyName && information[propertyName].length > 0) {
          index.isScolitoPresent = true;
        }
      });
    }
  }

  show(event, title) {
    if (event.srcElement.parentElement) {
      if (!event.srcElement.parentElement.classList.contains('noable')) {
        if (event.srcElement.classList.contains('link') || event.srcElement.classList.contains('content')) {
          if (this.activeName === title) {
            this.activeName = null;
          } else {
            this.activeName = title;
          }
        }
      }
    }
  }

  showPromotion(event, title) {
    if (event.srcElement.parentElement) {
      if (this.activeName === title) {
        this.activeName = null;
      } else {
        this.activeName = title;
      }
    }
  }

  showDetails(id) {
    this.submenu[id] = !this.submenu[id];
  }

  showAmazonDetails() {
    this.showedDetails = !this.showedDetails;
  }

  showModifica(id) {
    this.pdfActive[id] = !this.pdfActive[id];
  }

  getUtility(name) {
    const array = [];
    if (this.alternativeName[name]) {
      this.alternativeName[name].forEach(servName => {
        if (this.allServices) {
          const clonedServices = Object.assign([], this.allServices);
          clonedServices.filter(service => service.serviceName === servName)
            .map(service => {
              service.utilities = service.utilities.filter(utility => {
                if (utility.status === ServiceStatus.ATTIVATO || utility.status === ServiceStatus.IN_ATTIVAZIONE ||
                  utility.status === MobileRecordStatus.ACTIVE || utility.status === MobileRecordStatus.INITIACTIVE ||
                  utility.status === MobileRecordStatus.IN_ATTESA_DI_RISPOSTA || utility.status === MobileRecordStatus.MOB_RICHIESTA_FALLITA
                ) {
                  this.pdf[utility.id] = {};
                  this.pdf[utility.id].pdfList = this.setPDFList(service.serviceName);
                }
                return utility.status;
              });
              array.push(service.utilities);
              return service;
            });
        }
      });
    }

    return array;
  }

  setPDFList(serviceName) {
    return this.homeServices.getPDFList(serviceName, this.userCluster);
  }


  ngOnInit(): void {
    this.clientOffers.subscribe(
      (response: any) => {
        if (response.length > 0) {

          const active = response.filter(value => (new Date().getTime() < value.scadenzaAnnoContrattuale || value.scadenzaAnnoContrattuale == null));
          if (active.length > 0 && active.length < 2) {
            this.payData = {
              tuttoInUno: (active[0].canoneMensile).toFixed(2),
              contoRelax: (active[0].saldoContoRelax).toFixed(2)
            };
          }
        }
      });
    this.hasTutoInUnoActive = this.clientOffers.map(items => items.filter(value => value.adsl || value.ee || value.gas
      || value.voce)).map(offers => offers.length > 0);
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.servicesSubscription, this.userDataSubscription,
      this.clientOffersSubscription]);
  }

}
