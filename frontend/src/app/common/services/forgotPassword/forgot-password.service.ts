import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {ForgotPasswordResponse} from '../../model/forgotPassword/forgotPasswordResponse';
import {ForgotPasswordRequest} from '../../model/forgotPassword/forgotPasswordRequest';
import {HttpService} from '../../../services/http.service';
import {UserEntity} from '../../model/user/UserEntity';


@Injectable()
export class ForgotPasswordService {
  urlSetPsw = 'psw/setPassword';

  constructor(private httpClient: HttpClient, private httpService: HttpService) {
  }

  restorePassword(request: ForgotPasswordRequest): Observable<ForgotPasswordResponse> {
    return this.httpClient.post<ForgotPasswordResponse>(`/psw/forgotPassword`, request);
  }

  isTokenAvaliable(token: string): Observable<UserEntity[]> {
    return this.httpClient.get<UserEntity[]>(`/psw/isExpired?access_token=${token}`);
  }


  public setPsw(forgotPswData: any, token: string): Observable<ForgotPasswordResponse>  {
    const body = JSON.stringify(forgotPswData);
    const headers = new HttpHeaders()
      .set('Content-Type', 'application/json')
      .set('Auth', token);
    const options = {
      headers: headers
    };
    return this.httpClient.post<ForgotPasswordResponse>(this.urlSetPsw, body, options);
  }
}
