import {Injectable} from '@angular/core';
import {HttpBackend, HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {IPInfo} from '../../model/otp/IPInfo';

@Injectable()
export class OtpService {

  private http: HttpClient;
  private ip = 'null';

  constructor(handler: HttpBackend) {
    this.http = new HttpClient(handler);
  }

  sendOTP(): Observable<any> {
    /*return this.obtainIPInfo().map(data => {
      this.ip = data.ip;
      return this.http.post(`/api/otp/${localStorage.getItem('clientId')}/ip/${this.ip}`, {}).subscribe();
    });*/

     return this.http.post(`/api/otp/${localStorage.getItem('clientId')}/ip/${this.ip}`, {});
  }

  checkOTP(otp: string): Observable<any> {
    return this.http.get(`/api/otp/${localStorage.getItem('clientId')}/${otp}`);
  }

  obtainIPInfo(): Observable<IPInfo> {
    return this.http.get<IPInfo>('/api/ip/get');
  }
}
