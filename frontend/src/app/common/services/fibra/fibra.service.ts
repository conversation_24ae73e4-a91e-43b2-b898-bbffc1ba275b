import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs/Observable';
import { PodDetail } from '../../model/adsl/PodDetail';

@Injectable()
export class FibraService {

  constructor(private httpClient: HttpClient) {
  }

  getPodDetails(clientId: string, pod = ''): Observable<Array<PodDetail>> {
    return this.httpClient.get<Array<PodDetail>>(`/api/adsl/pod/details/${clientId}/${pod}`);
  }

}
