import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs/Observable';
import {RouterData} from '../../model/router/routerData';

@Injectable()
export class ConfigurazioneRouterService {
  // private clientId = localStorage.getItem('clientId');
  constructor(private httpClient: HttpClient) {
  }

  loadRouterInfo(clientId: string): Observable<Array<RouterData>> {
    const headers = new HttpHeaders({'clientid': clientId});
    return this.httpClient.get<Array<RouterData>>('api/routerInfo', {headers: headers});
  }
}
