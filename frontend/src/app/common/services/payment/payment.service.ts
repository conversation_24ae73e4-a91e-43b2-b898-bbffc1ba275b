import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import SubscriptionAutoricaricaRequest, {AutoricaricaInformationResponse, DeactivateOrActivateAutoRicaricaRequest} from '../../../routes/mobile/models/PaymentAutoricaricaModels';
import {ActivatePayPalAutoricaricaRequest, ConfirmPaymentModelRequest, ConfirmPaymentModelResponse, NexiPaymentRequest, NexiPaymentResponse, PayPalResponse} from '../../../routes/profilePage/model/ConfirmPaymet';
import {NexiAutoricaricaRequest} from '../../../routes/profilePage/model/NexiAutoricaricaRequest';

@Injectable()
export class PaymentService {

  constructor(private http: HttpClient) {
  }

  postPagamenti(body: ConfirmPaymentModelRequest): Observable<ConfirmPaymentModelResponse> {
    return this.http.post<ConfirmPaymentModelResponse>('/api/properties/payment/pagamenti', body);
  }

  activateAutoricaricaWithNexi(request: NexiAutoricaricaRequest): Observable<NexiPaymentResponse> {
    return this.http.post<NexiPaymentResponse>('/api/properties/payment/autoricarica/activate', request);
  }

  postPayPalAutoricarica(body: ActivatePayPalAutoricaricaRequest): Observable<PayPalResponse> {
    return this.http.post<PayPalResponse>('/api/properties/payment/autoricarica/paypal/activate', body);
  }

  postPaymentAutoricaricaInformation(body: SubscriptionAutoricaricaRequest): Observable<AutoricaricaInformationResponse> {
    return this.http.post<AutoricaricaInformationResponse>('/api/properties/payment/autoricarica/information', body);
  }

  postDeactivateAutoricarica(body: DeactivateOrActivateAutoRicaricaRequest): Observable<any> {
    return this.http.post<any>('/api/properties/payment/autoricarica/deactivate', body);
  }

  topUpSimWithNexi(request: NexiPaymentRequest): Observable<NexiPaymentResponse> {
    return this.http.post<NexiPaymentResponse>('/api/properties/payment/nexi', request);
  }

  getLocationString(amountOfMoney, simNumber): Observable<string> {
    return this.http.get(`/api/properties/payment/mobile?simNumber=${simNumber}&amount=${amountOfMoney}&clientId=${
      localStorage.getItem('clientId')}&access_token=${localStorage.getItem('access_token')}`, {responseType: 'text'});
  }

  loadScript(url: string) {
    const node = document.createElement('script');
    node.src = url;
    node.type = 'text/javascript';
    node.async = true;
    node.charset = 'utf-8';
    document.getElementsByTagName('head')[0].appendChild(node);
  }
}
