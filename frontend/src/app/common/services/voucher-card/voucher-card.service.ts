import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {RedeemVoucher, TopUpSimByVoucher, VoucherCardResponse, VoucherContractsInformation, VoucherInformationResponse, VoucherSimBalanceInformation} from '../../../routes/profilePage/model/RedeemVoucher';

@Injectable()
export class VoucherCardService {

  constructor(private httpClient: HttpClient) {
  }

  checkVoucherCard(cardNumber: string): Observable<VoucherCardResponse> {
    return this.httpClient.get<VoucherCardResponse>(`/api/voucher-card?voucher=${cardNumber}&clientId=${localStorage.getItem('clientId')}`);
  }

  redeemVoucherCard(redeemVoucher: RedeemVoucher): Observable<VoucherCardResponse> {
    return this.httpClient.post<VoucherCardResponse>(`/api/voucher-card/redeem`, redeemVoucher);
  }

  redeemVoucherCardForTopUpSim(redeemVoucher: TopUpSimByVoucher): Observable<any> {
    return this.httpClient.post<any>(`/api/voucher-card/top-up-sim?clientId=${localStorage.getItem('clientId')}`, redeemVoucher);
  }

  getInformationAboutUserCards(typeCard: string): Observable<VoucherInformationResponse> {
    return this.httpClient.get<VoucherInformationResponse>(`/api/voucher-card/information?typeCard=${typeCard}&clientId=${localStorage.getItem('clientId')}`);
  }

  getContractsInformation(): Observable<VoucherContractsInformation> {
    return this.httpClient.get<VoucherContractsInformation>(`/api/voucher-card/contracts?clientId=${localStorage.getItem('clientId')}`);
  }

  getSimBalanceInformation(utenza: string): Observable<VoucherSimBalanceInformation> {
    return this.httpClient.get<VoucherSimBalanceInformation>(`/api/voucher-card/sim-balance?simNumber=${utenza}&clientId=${localStorage.getItem('clientId')}`);
  }
}
