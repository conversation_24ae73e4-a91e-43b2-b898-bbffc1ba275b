import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';
import {ClientNotification} from '../../model/client-notification/client-notification.model';

@Injectable()
export class ClientNotificationService {

  readonly url = 'api/notifications/';

  constructor(private httpClient: HttpClient) {
  }

  getClientNotifications(): Observable<ClientNotification[]> {
    const clientId = this.setClientId();
    const headers = new HttpHeaders({'clientid': clientId});
    return this.httpClient.get<ClientNotification[]>(this.url + clientId, {headers: headers});
  }

  getAllClientNotificationsForUserList(clientIdList: number[]): Observable<ClientNotification[]> {
    const clientId = this.setClientId();
    const headers = new HttpHeaders({'clientid': clientId});
    return this.httpClient.post<ClientNotification[]>(this.url + clientId + '/list', clientIdList, {headers: headers});
  }

  getClientNotificationById(notificationId: number): Observable<ClientNotification> {
    const clientId = this.setClientId();
    const headers = new HttpHeaders({'clientid': clientId});
    return this.httpClient.get<ClientNotification>(this.url + clientId + '/' + notificationId, {headers: headers});
  }

  createOrUpdateClientNotification(clientNotification: ClientNotification): Observable<ClientNotification> {
    return this.httpClient.post<ClientNotification>(this.url, clientNotification, {
      headers: new HttpHeaders().set('disable-spinner', 'true')
    });
  }

  deleteClientNotification(notificationId: number): void {
    const clientId = this.setClientId();
    const headers = new HttpHeaders({'clientid': clientId});
    this.httpClient.delete<ClientNotification>(this.url + notificationId, {headers: headers});
  }

  getCondominioDebtNotifications(): Observable<ClientNotification[]> {
    const clientId = this.setClientId();
    const headers = new HttpHeaders({'clientid': clientId});
    return this.httpClient.get<ClientNotification[]>(this.url + clientId + '/condominio-debt', {headers: headers});
  }

  setClientId() {
    return localStorage.getItem('clientId');
  }

}
