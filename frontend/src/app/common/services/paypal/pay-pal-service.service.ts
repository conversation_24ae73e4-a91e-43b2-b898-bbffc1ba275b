import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';

@Injectable()
export class PayPalService {

  readonly urlFattura = '/api/paypal/';
  readonly urlRicarica = '/api/paypal/ricarica';
  readonly urlDilazioni = '/api/paypal/dilazioni';

  constructor(private http: HttpClient) {
  }

  public postPayPalActivation(body: any): Observable<any> {
    return this.http.post<any>(this.urlFattura, body);

  }

  public postPayPalActivationRicarica(body: any): Observable<any> {
    return this.http.post<any>(this.urlRicarica, body);
  }

  public postPayPalActivationDilazioni(body: any): Observable<any> {
    return this.http.post<any>(this.urlDilazioni, body);
  }
  public isOptimaNumber(simNumber): Observable<boolean> {
    return this.http.get<boolean>(`/api/paypal/isOptimaNumber?simNumber=${simNumber}&clientId=${
      localStorage.getItem('clientId')}`);
  }
}
