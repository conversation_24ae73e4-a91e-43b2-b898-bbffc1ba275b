
import {NotificaMdpInfo} from '../../model/notifica-mdp/NotificaMdpInfo';
import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';


@Injectable()
export class NotificaMdpService {

  constructor(private httpClient: HttpClient) {
  }

  getNotificationMdpIfClientExist(clientId: string): Observable<NotificaMdpInfo> {
    return this.httpClient.get<NotificaMdpInfo>(`/api/notifica/mdp/info?clientId=${clientId}`);
  }
}
