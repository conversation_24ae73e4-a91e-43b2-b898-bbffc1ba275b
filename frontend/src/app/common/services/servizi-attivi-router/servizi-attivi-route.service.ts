import { Injectable } from '@angular/core';
import { Router } from '@angular/router';


@Injectable()
export class ServiziAttiviRouteService {
  url = '/faidate/servizi-attivi';
  route = {
    'MOBILE': '/mobile/ricarica/',
    'ENERGIA': '/energia/',
    'GAS': '/gas/',
    'WLR': '/wlr/',
    'VOCE': '/wlr/',
    'VOIP': '/wlr/',
    'ADSL': '/adsl/',
    'Amazon Prime': '/amazon-prime',
    'Teleconsulto medico': '/teleconsulto-medico',
    'Tutela Legale': '/tutela-legale',
    'Assistenza H24': '/assistenza-h24',
    'Total Security': '/total-security',
    'Safe Call': '/safe-call',
  };

  constructor(public router: Router) {
  }

  setRoute(serviceName, utNumber) {
      this.router.navigate([this.url + this.route[serviceName] + utNumber]);
  }
}
