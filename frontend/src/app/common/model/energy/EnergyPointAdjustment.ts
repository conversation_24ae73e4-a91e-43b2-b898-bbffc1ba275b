export interface EnergyPointAdjustment {
  anno: number;
  energiaReattivaEntro: number;
  energiaReattivaOltre: number;
  grossistaDesc: string;
  grossistaId: number;
  invoiceID: number;
  ivaConguaglio: number;
  ivaConguaglioDescrizione: string;
  kwh: number;
  kwhinID: number;
  mese: number;
  modulo: string;
  nota: string;
  potenza: number;
  provenienza: number;
  serie: string;
  tariffaTrasportoCode: string;
  tariffaTrasportoDesc: string;
  tariffaTrasportoID: number;
  tipo: number;
  tipoContatoreDesc: string;
  tipoContatoreId: number;
  user: string;
  utilizzato?: null;
  dataLettura: any;
}
export const EnergyPointAdjustmentObject = (year: number, dataLettura: any, kwh: number): EnergyPointAdjustment => {
  return {
    anno: year,
    dataLettura: dataLettura,
    energiaReattivaEntro: 0,
    energiaReattivaOltre: 0,
    grossistaDesc: '',
    grossistaId: 0,
    invoiceID: 0,
    ivaConguaglio: 0,
    ivaConguaglioDescrizione: '',
    kwh: kwh,
    kwhinID: 0,
    mese: 0,
    modulo: '',
    nota: '',
    potenza: 0,
    provenienza: 0,
    serie: '',
    tariffaTrasportoCode: '',
    tariffaTrasportoDesc: '',
    tariffaTrasportoID: 0,
    tipo: 0,
    tipoContatoreDesc: '',
    tipoContatoreId: 0,
    user: ''
  };
};

export interface EnergyDetails {
  IdConsumo: number;
  dataConsumo: string;
  ka: number;
  kp: number;
  kr: number;
  ora1: number;
  ora2: number;
  ora3: number;
  ora4: number;
  ora5: number;
  ora6: number;
  ora7: number;
  ora8: number;
  ora9: number;
  ora10: number;
  ora11: number;
  ora12: number;
  ora13: number;
  ora14: number;
  ora15: number;
  ora16: number;
  ora17: number;
  ora18: number;
  ora19: number;
  ora20: number;
  ora21: number;
  ora22: number;
  ora23: number;
  ora24: number;
}
