import {Cluster} from '../enum/Cluster';
import {PaymentData} from './paymentData.model';

export class UserData {
  firstName: string;
  lastName: string;
  vatNumber: string;
  fiscalCode: string;
  email: string;
  passwordIdentification: string;
  id: number;
  residenceAddress: ResidenceAddress;
  cluster: Status;
  sottotipoCluster: string;
  // modalitaPagamento: string;
  paymentData: PaymentData;
  nameInInvoice: string;
  billingType: string;
  addresses: Address[];
  pec: string;
  hasPasswordPec: any;
  phoneNumber: string;
  clienteConvertibile?: boolean;
  mobileNumber?: string;
  recipientCode?: string;
  DocumentType?: string;
  DocumentNumber?: string;
  deputy?: Deputy
}

export interface ResidenceAddress {
  address: string;
  city: string;
  postalCode: number;
  country: string;
  province: string;
  shortenedProvince: string;
}

export class Status {
  key: string;
  value: string | Cluster;
}

export class BillingAddress {
  code: string;
  description: string;

  constructor(str) {
    this.code = JSON.parse(str) && JSON.parse(str).code;
    this.description = JSON.parse(str) && JSON.parse(str).description;
  }
}

export class Address {
  tipo: string;
  indirizzo: string;
}

export class Deputy{
  firstName: string;
  lastName: string;
  fiscalCode: string;
  dateOfBirth: string;
  sex: string;
  cityOfBirth: string;
  documentType: string;
  documentNumber: string;
}
/*export class CreditCardStatus {
  isEnabled: boolean;
}*/
