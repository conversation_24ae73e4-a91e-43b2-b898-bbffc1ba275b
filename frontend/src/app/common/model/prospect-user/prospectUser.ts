import {ErrorStatus} from '../ChangePersonalDataResponse';

export default class ProspectUserRegisterForm {
  CF: string;
  email: string;
  contractId: number;
  password1: string;
  password2: string;

  constructor(CF: string, email: string, contractId: number, password1: string, password2: string) {
    this.CF = CF;
    this.email = email;
    this.contractId = contractId;
    this.password1 = password1;
    this.password2 = password2;
  }
}

export class ProspectUserContractsInformation {
  errorStatus: ErrorStatus;
  response: ProspectUserContract[];
}

export class ProspectUserContract {
  idContratto: number;
  idGruppo: number;
  dataArrivoFax: Date;
  dataImportazione: Date;
  dataStipula: Date;
  idCliente: number;
  nome: string;
  cognome: string;
  email: string;
  servizio: number;
  utenza: string;
  icona: string;
  categorieCCN: CategoryCCN;
  documentiDaRecuperare: string;
  invioEmail: string;
  azioneQCC: string;
  motivazioneQCCKO: number;
  attivazioneParziale: number;
  serviziCestinati: string;
  flagContrattoCestinato: boolean;
  dataInvioAttivazione: Date;
  // additional fields
  isSentDocuments: boolean;
  isBookedCall: boolean;
}

export enum CategoryCCN {
  Category1 = 1,
  Category2 = 2,
  Category3 = 3
}

export class ShortInformationProspectUserContract {
  idContratto: number;
  idGruppo: number;
  dataStipula: Date;
  luce: boolean;
  gas: boolean;
  internet: boolean;
  fisso: boolean;
  mobile: boolean;
  teleconsulto: boolean;
  assistenza: boolean;
  consulenzaLegale: boolean;

  constructor(prospectUserContract: ProspectUserContract) {
    this.idContratto = prospectUserContract.idContratto;
    this.idGruppo = prospectUserContract.idGruppo;
    this.dataStipula = prospectUserContract.dataStipula;

    const conditions = [
      {iconaValue: 'EE', property: 'luce'},
      {iconaValue: 'GAS', property: 'gas'},
      {iconaValue: 'INTERNET', property: 'internet'},
      {iconaValue: 'VOCE', property: 'fisso'},
      {iconaValue: 'MOBILE', property: 'mobile'},
      {iconaValue: 'YH', property: 'teleconsulto'},
      {iconaValue: 'H24', property: 'assistenza'},
      {iconaValue: 'TUTELALEGALE', property: 'consulenzaLegale'},
    ];

    conditions.forEach((condition) => {
      this[condition.property] = prospectUserContract.icona === condition.iconaValue;
    });
  }
}

export class EmailToSupportWithFiles {
  destinations: Destination[] = [];
  ccs: Destination[] = [];
  ccns = null;
  bodyText: string;
  bodyContentType = 'text/html';
  objectText: string;
  isPec = false;
  attachmentsEmailStream = null;

  constructor(email: string, objectText: string, bodyText: string, invioEmail: string) {
    this.destinations.push(new Destination(email));
    this.objectText = objectText;
    this.bodyText = toBase64(bodyText);
    this.ccs.push(new Destination(invioEmail));
  }
}

function toBase64(str) {
  return btoa(
    new Uint8Array(
      str.split('').map(char => char.charCodeAt(0))
    ).reduce((data, byte) => data + String.fromCharCode(byte), '')
  );
}

export class Destination {
  address: string;

  constructor(address: string) {
    this.address = address;
  }
}

