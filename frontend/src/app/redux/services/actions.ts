import { Type } from './types';
import { dispatch, select } from '@angular-redux/store';
import { Injectable } from '@angular/core';
import { UserServices } from '../../common/model/services/userServices.model';
import { Observable } from 'rxjs/Observable';
import ServiceStateModel from '../model/ServiceStateModel';
import { ServiceType } from '../../common/enum/Service';
import { ServiceUtils } from '../../common/utils/ServiceUtils';
import { NormalizeUtils } from '../../common/utils/NormalizeUtils';


@Injectable()
export class ServicesActions {

  @select(['services'])
  services: Observable<ServiceStateModel>;

  serviceState: ServiceStateModel;

  actionsMap = {};

  constructor() {
    this.services.subscribe(serviceState => {
      this.serviceState = serviceState;
    });
    this.initializeActionsMap();
  }

  @dispatch()
  servicesLoading = () => ({type: Type.SERVICES_LOADING})

  @dispatch()
  servicesLoaded = (services: Array<UserServices> = []) => {
    const activeServices = ServiceUtils.getActiveServices(services);
    const inactiveServices = ServiceUtils.getInActiveServices(services);
    return {
      type: Type.SERVICES_LOADED,
      activeServices,
      inactiveServices,
      services,
      hasActiveServices: Object.keys(activeServices).length > 0,
      hasInactiveServices: Object.keys(inactiveServices).length > 0,
      normalizedServices: NormalizeUtils.normalizeList(services, 'serviceName')
    };
  }

  @dispatch()
  loadLucePodDetails = (service: UserServices) => ({type: Type.ENERGIA_POD_DETAILS_LOADING, service})

  @dispatch()
  loadGasAdditionalPodDetails = (service: UserServices) => ({type: Type.GAS_POD_DETAILS_LOADING, service})

  @dispatch()
  loadInternetPodDetails = (clientId: string) => ({type: Type.INTERNET_POD_DETAILS_LOADING, clientId})

  @dispatch()
  loadFissoPodDetails = (clientId) => ({type: Type.FISSO_POD_DETAILS_LOADING, clientId})

  @dispatch()
  loadAmazonPrimePodDetails = (clientId) => ({type: Type.AMAZON_PRIME_POD_DETAILS_LOADING, clientId})

  @dispatch()
  loadRouterInfo = (clientId) => ({type: Type.ROUTER_INFO_LOADING, clientId})

  loadGasAdditionalDetailsIfNotExist(service) {
    if (!this.serviceState.gasPodDetailsLoading && !this.serviceState.gasPodDetailsLoaded) {
      const found = this.serviceState.normalizedServices[service];
      if (found) {
        this.loadGasAdditionalPodDetails(found);
      }
    }
  }

  loadInternetPodDetailsIfNotExist() {
    if (!this.serviceState.internetPodDetailsLoading && !this.serviceState.adslPodDetailsLoaded) {
      this.loadInternetPodDetails(localStorage.getItem('clientId'));
    }
  }

  loadLucePodDetailsIfNotExist(service) {
    if (!this.serviceState.lucePodDetailsLoading && !this.serviceState.lucePodDetailsLoaded) {
      const found = this.serviceState.normalizedServices[service];
      if (found) {
        this.loadLucePodDetails(found);
      }
    }
  }

  loadFissoPodDetailsIfNotExist() {
    if (!this.serviceState.fissoPodDetailsLoading && !this.serviceState.fissoPodDetailsLoaded) {
      this.loadFissoPodDetails(localStorage.getItem('clientId'));
    }
  }

  loadAmazonPrimePodDetailsIfNotExist() {
    if (!this.serviceState.amazonprimePodDetailsLoading && !this.serviceState.amazonprimePodDetailsLoaded) {
      this.loadAmazonPrimePodDetails(localStorage.getItem('clientId'));
    }
  }

  initializeActionsMap() {
    const {loadInternetPodDetailsIfNotExist, loadFissoPodDetailsIfNotExist, loadLucePodDetailsIfNotExist,
      loadGasAdditionalDetailsIfNotExist, loadAmazonPrimePodDetailsIfNotExist} = this;
    this.actionsMap = {};
    this.actionsMap[ServiceType.INTERNET] = loadInternetPodDetailsIfNotExist.bind(this);
    this.actionsMap[ServiceType.TELEPHONE] = loadFissoPodDetailsIfNotExist.bind(this);
    this.actionsMap[ServiceType.ENERGY] = loadLucePodDetailsIfNotExist.bind(this);
    this.actionsMap[ServiceType.GAS] = loadGasAdditionalDetailsIfNotExist.bind(this);
    this.actionsMap[ServiceType.AMAZON] = loadAmazonPrimePodDetailsIfNotExist.bind(this);
  }

  loadServiceDetails(service: string) {
    const serviceType = ServiceUtils.getServiceType(service);
    if (serviceType) {
      this.actionsMap[serviceType](service);
    }
  }
}
