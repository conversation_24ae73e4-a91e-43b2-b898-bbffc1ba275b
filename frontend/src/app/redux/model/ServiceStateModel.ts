import {UserServices} from '../../common/model/services/userServices.model';

export default class ServiceStateModel {
  services: Array<UserServices> = [];
  activeServices = {};
  inactiveServices = {};
  normalizedServices = {};
  lucePodDetails = {};
  gasPodDetails = {};
  adslPodDetails = {};
  activeAdslPodDetails = {};
  fissoPodDetails = {};
  amazonprimePodDetails = {};
  routerInfo = {};
  hasActiveServices: boolean;
  hasInactiveServices: boolean;
  servicesLoaded: boolean;
  lucePodDetailsLoaded: boolean;
  lucePodDetailsLoading: boolean;
  gasPodDetailsLoading: boolean;
  gasPodDetailsLoaded: boolean;
  internetPodDetailsLoading: boolean;
  adslPodDetailsLoaded: boolean;
  fissoPodDetailsLoading: boolean;
  fissoPodDetailsLoaded: boolean;
  amazonprimePodDetailsLoading: boolean;
  amazonprimePodDetailsLoaded: boolean;
  routerInfoLoaded: boolean;
  routerInfoLoading: boolean;
}
