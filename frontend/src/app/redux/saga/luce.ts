import { getContext, put, takeEvery } from 'redux-saga/effects';
import { Type } from '../services/types';
import { NormalizeUtils } from '../../common/utils/NormalizeUtils';
import { EnergiaService } from '../../common/services/energia/energia-service.service';


function* loadEnergyPodDetails(action) {
  const context = yield getContext('context');
  const service = context.get(EnergiaService);
  const podDetails = yield (<EnergiaService>service).loadPodDetailsByUserService(action.service).toPromise();
  yield put({type: Type.ENERGIA_POD_DETAILS_LOADED, podDetails: NormalizeUtils.normalizeList(podDetails, 'descrizione')});
}


export function* energyRootSaga() {
  yield takeEvery(Type.ENERGIA_POD_DETAILS_LOADING, loadEnergyPodDetails);
}
