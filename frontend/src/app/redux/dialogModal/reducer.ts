import DialogModalStateModel from '../model/DialogModalStateModel';
import {Type} from './types';

const initialState = new DialogModalStateModel();

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case Type.SHOW_DIALOG_MODAL:
      return Object.assign({}, {show: true, dialogModalEntity: action.dialogModalEntity});
    case Type.HIDE_DIALOG_MODAL:
      return Object.assign({}, {show: false});
    default:
      return state;
  }
}
