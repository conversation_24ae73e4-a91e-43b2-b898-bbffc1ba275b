import { Injectable } from '@angular/core';
import { HttpBackend, HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs/Observable';
import ChatSkillSet from '../../model/response/ChatSkillset';
import GetAndUpdateAnonymousCustomerIdRequest from '../../model/request/GetAndUpdateAnonymousCustomerIdRequest';
import CICustomerReadType from '../../model/response/CICustomerReadType';
import GetSkillSetByNameRequest from '../../model/request/GetSkillSetByNameRequest';
import RequestTextChat from '../../model/request/RequestTextChat';
import CIContactWriteType from '../../model/response/CIContactWriteType';
import CIDateTime from '../../model/response/CIDateTime';
import UpdateAliveTimeRequest from '../../model/request/UpdateAliveTimeRequest';
import GetContactOnHoldMessages from '../../model/GetContactOnHoldMessages';
import ReadChatMessageRequest from '../../model/request/ReadChatMessageRequest';
import ChatMessage from '../../model/response/ChatMessage';
import CICustomFieldWriteType from '../../model/response/CICustomFieldWriteType';
import GetWebOnHoldURLsRequest from '../../model/request/GetWebOnHoldURLsRequest';
import MultipleChatMessage from '../../model/response/MultipleChatMessage';
import SessionKey from '../../model/response/SessionKey';
import { SetupTextChatResponse } from '../../model/response/SetupTextChatResponse';
import AbandonWaitingQueueResponse from '../../model/response/AbandonWaitingQueueResponse';
import { AbandonWaitingQueueRequest } from '../../model/request/AbandonWaitingQueueRequest';
import { SendNewMessageStatus } from '../../model/response/SendNewMessageStatus';
import { CustomerInfo } from '../../model/response/CustomerInfo';
import ReadCustomerInfoRequest from '../../model/request/ReadCustomerInfoRequest';
import { LogoffResponse } from '../../model/response/LogoffResponse';
import LogoffRequest from '../../model/request/LogoffRequest';
import { CICOnHoldMessages } from '../../model/response/CICOnHoldMessages';
import Message from '../../model/request/Message';
import ChatUser from '../../model/ChatUser';
import { config } from '../../config';

import 'rxjs/add/operator/skip';

@Injectable()
export class ChatService {
  private httpClient: HttpClient;

  constructor(
    handler: HttpBackend
  ) {
    this.httpClient = new HttpClient(handler);
  }

  private options: object = {
    headers: new HttpHeaders().set('Content-Type', 'application/json')
      .set('Authorization', localStorage.getItem('access_token')),
  };

  getAnonymousSessionKey(): Observable<SessionKey> {
    return this.httpClient.get<SessionKey>('api/chat/anonymous/session/key', this.options);
  }

  getAndUpdateAnonymousCustomerID(anonymousSessionKey: SessionKey, user: ChatUser): Observable<object> {
    if (!anonymousSessionKey && !user) {
      return Observable.throw('Missed initialization data.');
    }
    const body = new GetAndUpdateAnonymousCustomerIdRequest();
    body.loginResult = anonymousSessionKey;
    body.emailAddress = user.email;
    body.phoneNumber = user.phoneNumber;
    const cICustomerReadType = new CICustomerReadType();
    cICustomerReadType.firstName = user.firstName;
    cICustomerReadType.lastName = user.lastName;
    cICustomerReadType.username = user.username;
    body.thisCustomer = cICustomerReadType;
    return this.httpClient.post<object>('api/chat/update/customer/id', body, this.options);
  }

  //
  readSkillSetByName(sessionKey: SessionKey): Observable<ChatSkillSet> {
    const body = new GetSkillSetByNameRequest();
    body.skillsetName = config.skillSet;
    body.sessionKey = sessionKey.sessionKey;
    return this.httpClient.post<ChatSkillSet>('api/chat/read/skillset', body, this.options);
  }

  setupTextChat(sessionKey: SessionKey, chatSkillSet: ChatSkillSet): Observable<SetupTextChatResponse> {
    const body = new RequestTextChat();
    body.newContact = new CIContactWriteType();
    body.newContact.skillsetID = chatSkillSet.id;
    body.newContact.timezone = 999;
    body.newContact.callbackTime = new CIDateTime();
    const cICustomFieldWriteType = new CICustomFieldWriteType();
    cICustomFieldWriteType.isTextVisible = true;
    cICustomFieldWriteType.name = '';
    cICustomFieldWriteType.text = '';
    body.newContact.customFields = [cICustomFieldWriteType];
    body.sessionKey = sessionKey.sessionKey;
    return this.httpClient.post<SetupTextChatResponse>('api/chat/setup/chat', body, this.options);
  }

  updateAliveTime(sessionKey: SessionKey, isTyping: boolean): Observable<CIDateTime> {
    const body = new UpdateAliveTimeRequest();
    body.sessionKey = sessionKey.sessionKey;
    body.isTyping = !!isTyping;
    return this.httpClient.post<CIDateTime>('api/chat/update/alive/time', body, this.options);
  }


  getOnHoldComfortMessages(sessionKey: SessionKey, contactId: number): Observable<CICOnHoldMessages[]> {
    const body = new GetContactOnHoldMessages();
    body.sessionKey = sessionKey.sessionKey;
    body.contactId = contactId;
    return this.httpClient.post<CICOnHoldMessages[]>('api/chat/get/comfort/message', body, this.options);
  }


  getHistory(sessionKey: SessionKey, lastReadTime): Observable<MultipleChatMessage> {
    const body = new ReadChatMessageRequest();
    body.lastReadTime = new CIDateTime();
    body.lastReadTime.milliseconds = lastReadTime;
    body.isWriting = false;
    body.sessionKey = sessionKey.sessionKey;
    return this.httpClient.post<MultipleChatMessage>('api/chat/get/history', body, this.options);
  }

  getWebOnHoldURLs(sessionKey: SessionKey, webOnHoldTagGroup: string) {
    const body = new GetWebOnHoldURLsRequest();
    body.sessionKey = sessionKey.sessionKey;
    body.tagName = webOnHoldTagGroup;
    return this.httpClient.post<ChatMessage>('api/chat/get/hold/urls', body, this.options);
  }

  removeChatContactFromWaitingQueue(sessionKey: SessionKey, comment: string, contactId: number): Observable<AbandonWaitingQueueResponse> {
    if (!sessionKey && sessionKey.sessionKey) {
      return null;
    }
    const body = new AbandonWaitingQueueRequest();
    body.sessionKey = sessionKey.sessionKey;
    body.closureComment = comment;
    body.contactID = contactId;
    return this.httpClient.post<AbandonWaitingQueueResponse>('api/chat/leave/queue', body, this.options);
  }

  sendChatMessage(chatMessage: Message): Observable<SendNewMessageStatus> {
    if (chatMessage) {
      return this.httpClient.post<SendNewMessageStatus>('api/chat/send/message', chatMessage, this.options);
    }
    return Observable.empty();
  }

  getCustomerInfo(sessionKey: SessionKey, customerId: number): Observable<CustomerInfo> {
    const body = new ReadCustomerInfoRequest();
    body.id = customerId;
    body.sessionKey = sessionKey.sessionKey;
    return this.httpClient.post<CustomerInfo>('api/chat/customer/info', body, this.options);
  }

  logoff(sessionKey: SessionKey, contactId: number, username: string): Observable<LogoffResponse> {
    const body = new LogoffRequest();
    body.sessionKey = sessionKey.sessionKey;
    body.contractId = contactId;
    body.username = username;
    return this.httpClient.post<LogoffResponse>('api/chat/close/chat', body, this.options);
  }

}
