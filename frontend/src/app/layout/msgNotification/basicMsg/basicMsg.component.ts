import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import Msg from '../../../common/model/Msg';
import {MessageGlobals} from '../../../common/utils/globalVariables/MessageGlobals';


@Component({
  selector: 'app-basic-msg',
  templateUrl: './basicMsg.component.html',
  styleUrls: ['./basicMsg.component.scss']
})
export class BasicMsgComponent implements OnInit, OnChanges {
 @Input() msg: Msg;
  constructor( private msgGlobals: MessageGlobals) {
  }

  ngOnInit() {

  }

  ngOnChanges(changes: SimpleChanges): void {
  }
}
