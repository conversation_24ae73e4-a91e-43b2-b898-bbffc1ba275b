.footerBgMobile{
  display: none;
  background: #8b8b8b;
  padding: 2%;
  color:white;
  text-align: center;
  img{
    margin:10px auto;
    width: 100px;
    height: 30px;
  }
  p {
    font-size: 15px;
    padding-top: 15px;
  }
}


.footerBg{
  background: #8b8b8b;
  color:white;
  padding: 2% 2% 15px;
  // margin-left: 250px;
  .footerLink{
    font-size: 13px;
    margin-top: 5px;
  }
  .brand-logo img {
    width: 130px;
    height: 60px;
  }
}
.footerBg a{
  color:white;
  text-decoration: underline;
}
.footerBg a:hover{
  color:white;

}
.footerBg p{
  padding-top:10px ;
}
@media only screen and (max-width: 800px){
  .footerBg{
    margin-left:0;
  }
}

@media only screen and (max-width: 767px) {
  .footerBg{
    display: none;
  }
  .footerBgMobile{
    display: block;
    margin-top: 20px;
  }
}


.app--assistant {
  text-decoration: none !important;
  text-align: right;
  padding-right: 330px;
  display: block;
}
