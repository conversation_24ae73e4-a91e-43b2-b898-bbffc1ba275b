import {Component, Input, OnInit, On<PERSON><PERSON>roy} from '@angular/core';
import {ClientNotificationService} from '../../common/services/client-notification/client-notification.service';
import {ClientNotification} from '../../common/model/client-notification/client-notification.model';
import {Router, NavigationEnd} from '@angular/router';
import {Subscription} from 'rxjs/Subscription';
import 'rxjs/add/operator/filter';
import * as moment from 'moment';

moment.locale('it');

@Component({
  selector: 'app-client-notification',
  templateUrl: './client-notification.component.html',
  styleUrls: ['./client-notification.component.scss']
})
export class ClientNotificationComponent implements OnInit, OnDestroy {

  @Input() notificationList: ClientNotification[] = [];

  isReadNotificationList: ClientNotification[] = [];
  unReadNotificationList: ClientNotification[] = [];
  clientNotificationList: ClientNotification[] = [];
  debtNotificationList: ClientNotification[] = [];
  readDebtNotifications: string[] = [];

  isPanelOpen = false;
  expandedNotificationId: number | null = null;
  showModalWindow: boolean;
  isOnCondominioPage = false;
  private routerSubscription: Subscription;

  constructor(private readonly clientNotificationService: ClientNotificationService, private readonly router: Router) {
  }

  ngOnInit(): void {
    this.checkCurrentRoute();
    this.subscribeToRouteChanges();
    if (this.notificationList.length) {
      this.initializeNotifications(this.notificationList);
    } else {
      this.loadNotifications();
    }
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  private checkCurrentRoute(): void {
    this.isOnCondominioPage = this.router.url === '/gestione-condomini';
  }

  private subscribeToRouteChanges(): void {
    this.routerSubscription = this.router.events
      .filter(event => event instanceof NavigationEnd)
      .subscribe((event: NavigationEnd) => {
        const wasOnCondominioPage = this.isOnCondominioPage;
        this.isOnCondominioPage = event.url === '/gestione-condomini';
        if (wasOnCondominioPage && !this.isOnCondominioPage) {
          this.clearDebtNotifications();
        } else if (!wasOnCondominioPage && this.isOnCondominioPage) {
          this.loadDebtNotifications();
        }
      });
  }

  private loadNotifications(): void {
    this.clientNotificationService.getClientNotifications().subscribe(notificationList => {
      this.initializeNotifications(notificationList);
      if (this.isOnCondominioPage) {
        this.loadDebtNotifications();
      }
    });
  }

  private loadDebtNotifications(): void {
    this.clientNotificationService.getCondominioDebtNotifications().subscribe(debtNotifications => {
      debtNotifications.forEach((notification, index) => {
        notification.isDebtNotification = true;
        notification.debtCustomerId = notification.clientId;
        notification.id = parseInt(notification.clientId);
        notification.readDate = null;
      });
      this.debtNotificationList = debtNotifications;
      this.updateNotificationList();
    });
  }

  private clearDebtNotifications(): void {
    this.debtNotificationList = [];
    this.readDebtNotifications = [];
    this.updateNotificationList();
  }

  private initializeNotifications(notificationList: ClientNotification[]): void {
    this.parseNotificationList(notificationList);
    this.updateNotificationList();
    this.checkIfUnreadNotificationExist();
  }

  private parseNotificationList(notificationList: ClientNotification[]): void {
    notificationList.forEach(notification => {
      const expirationDate = moment(notification.expire_date).startOf('day');
      const insertionDate = moment(notification.insertDate).add(notification.duration, 'days').startOf('day');
      if (moment().startOf('day').isSameOrBefore(expirationDate) || moment().startOf('day').isSameOrBefore(insertionDate)) {
        if (notification.readDate) {
          this.isReadNotificationList.push(notification);
        } else {
          this.unReadNotificationList.push(notification);
        }
      }
    });
  }

  private updateNotificationList(): void {
    const unreadDebtNotifications = this.debtNotificationList.filter(n => !n.readDate);
    const readDebtNotifications = this.debtNotificationList.filter(n => n.readDate);
    this.clientNotificationList = [...this.unReadNotificationList, ...unreadDebtNotifications,
      ...this.isReadNotificationList, ...readDebtNotifications];
  }

  checkIfUnreadNotificationExist(): void {
    if (this.unReadNotificationList.length === 1) {
      this.splitMessage(this.unReadNotificationList[0]);
      this.showModalWindow = true;
    }
  }

  openNotification(clientNotification: ClientNotification): void {
    this.expandedNotificationId = this.expandedNotificationId === clientNotification.id ? null : clientNotification.id;
    if (!clientNotification.readDate) {
      if (clientNotification.isDebtNotification) {
        this.markDebtNotificationAsRead(clientNotification);
      } else {
        this.markNotificationAsRead(clientNotification);
      }
    }
    this.splitMessage(clientNotification);
  }

  private markNotificationAsRead(clientNotification: ClientNotification): void {
    clientNotification.insertDate = new Date(clientNotification.insertDate);
    clientNotification.readDate = new Date();
    const index = this.unReadNotificationList.findIndex(notification => clientNotification.id === notification.id);
    this.unReadNotificationList.splice(index, 1);
    this.isReadNotificationList.push(clientNotification);
    this.isReadNotificationList.sort((a, b) => {
      return new Date(b.insertDate).getTime() - new Date(a.insertDate).getTime();
    });
    this.clientNotificationService.createOrUpdateClientNotification(clientNotification).subscribe();
  }

  private splitMessage(notification: ClientNotification): void {
    const {textMarker, redirectUrl, message} = notification;
    if (notification.isDebtNotification && textMarker) {
      const startIndex = message.indexOf(textMarker);
      if (startIndex !== -1) {
        let firstPart = message.slice(0, startIndex).trim();
        firstPart = firstPart.replace(/\n/g, '<br>');
        notification.firstPartOfMessage = firstPart;
        notification.secondPartOfMessage = message.slice(startIndex + textMarker.length).trim();
      } else {
        notification.firstPartOfMessage = message.replace(/\n/g, '<br>');
        notification.secondPartOfMessage = '';
      }
      return;
    }

    if (!textMarker || !redirectUrl) {
      notification.firstPartOfMessage = '';
      notification.secondPartOfMessage = '';
      return;
    }
    const startIndex = message.indexOf(textMarker);
    if (startIndex === -1) {
      notification.firstPartOfMessage = message;
      notification.secondPartOfMessage = '';
      return;
    }
    notification.firstPartOfMessage = startIndex > 0 ? message.slice(0, startIndex).trim() : '';
    notification.secondPartOfMessage = message.slice(startIndex + textMarker.length).trim();
  }

  toggleNotificationPanel(): void {
    this.isPanelOpen = !this.isPanelOpen;
  }

  trackByNotificationId(index: number, notification: ClientNotification): number {
    return notification.id;
  }

  hideWindow(): void {
    this.showModalWindow = false;
    this.markNotificationAsRead(this.unReadNotificationList[0]);
  }

  onDebtNotificationClick(notification: ClientNotification): void {
    console.log(notification.clientId)
    if (notification.isDebtNotification && notification.clientId) {
      this.openNewUser(notification.clientId);
    }
  }

  openNewUser(clientId: string): void {
    localStorage.setItem('clientId', clientId);
    this.router.navigate(['/invoices/all']);
  }


  private markDebtNotificationAsRead(notification: ClientNotification): void {
    console.log(notification.clientId)
    notification.readDate = new Date();
    if (notification.debtCustomerId && this.readDebtNotifications.indexOf(notification.debtCustomerId) === -1) {
      this.readDebtNotifications.push(notification.debtCustomerId);
    }
  }

  getTotalUnreadCount(): number {
    const regularUnread = this.unReadNotificationList.filter(n => !n.readDate).length;
    const debtUnread = this.debtNotificationList.filter(n => !n.readDate).length;
    return regularUnread + debtUnread;
  }
}
