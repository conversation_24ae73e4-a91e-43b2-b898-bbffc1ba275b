@charset "UTF-8";
@font-face {
  font-family: "Lato-Medium";
  src: url("./lato/Lato-Medium.ttf") format("truetype");
}
@font-face {
  font-family: "Lato-Regular";
  src: url("./lato/Lato-Medium.ttf") format("truetype");
}
@font-face {
  font-family: "optimaicons";
  src:url("optimaicons.eot");
  src:url("optimaicons.eot?#iefix") format("embedded-opentype"),
  url("optimaicons.woff") format("woff"),
  url("optimaicons.ttf") format("truetype"),
  url("optimaicons.svg#optimaicons") format("svg");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'amazonEmberDisplay_Bd';
  src: url("AmazonEmberDisplay_Bd.ttf") format("truetype");
}
@font-face {
  font-family: 'amazonEmberDisplay_Rg';
  src: url('AmazonEmberDisplay_Rg.ttf') format("truetype");
}
[data-icon]:before {
  font-family: "optimaicons" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "optimaicons" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-question:before {
  content: "\61";
}
.icon-user:before {
  content: "\62";
}
.icon-car:before {
  content: "\63";
}
.icon-autoletture:before {
  content: "\64";
}
.icon-contorelax:before {
  content: "\65";
}
.icon-document:before {
  content: "\66";
}
.icon-documents:before {
  content: "\67";
}
.icon-envelope:before {
  content: "\68";
}
.icon-fisso {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Telefono.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
.icon-amazon-prime {
  background: url("/assets/img/amazon/amazon-prime-logo.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
/*.icon-amazon-prime-short {
  background: url("/assets/img/amazon/amazon-prime.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}*/
.icon-amazon-pacco {
  background: url("/assets/img/amazon/icona_Amazon_pacco.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
/*.icon-amazon-prime-logo {
  background: url("/assets/img/amazon-icons/amazon-prime-logo.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}*/
.icon-gas {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Gas.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
.icon-gear:before {
  content: "\6b";
}
.icon-internet {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Internet.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
.icon-kolibri:before {
  content: "\6d";
}
.icon-luce {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Luce.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
.icon-mobile {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_SIM.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
.icon-assicurazione {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Assicurazione.png") no-repeat center;
  background-size: contain;
  height: 50px;
  width: 50px;
}
.icon-assistenza-caldaia {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Assistenza_caldaia.png") no-repeat left;
  background-size: contain;
  height: 50px;
  /*width: 50px;*/
}
.icon-servizi-aggiuntivi{
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Servizi_Aggiutivi.png") no-repeat center;
  background-size: contain;
  width: 51px;
  height: 48px;
}
.amazon-short-icon {
  background: url("/assets/img/amazon/prime-logo.png") no-repeat center;
  background-size: contain;
  width: 39px;
  height: 37px;
}
.doctor-picture {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_telemedicina.png") no-repeat center;
  background-size: contain;
  width: 38px;
  height: 37px;
}
.assistance-picture {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Assistenza_H24.png") no-repeat center;
  background-size: contain;
  width: 37px;
  height: 37px;
}
.legal-protection-picture {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Tutela_Legale.png") no-repeat center;
  background-size: contain;
  width: 37px;
  height: 37px;
}
.total-security-picture {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Total_Security.png") no-repeat center;
  background-size: contain;
  width: 37px;
  height: 37px;
}
.safe-call-picture {
  background: url("/assets/img/optima/Set_Icone_AreaClienti_Safe_Call_big.png") no-repeat center;
  background-size: contain;
  width: 37px;
  height: 37px;
}
.icon-home:before {
  content: "\70";
}
.icon-arrow-down:before {
  content: "\71";
}
.icon-eye:before {
  content: "\72";
}
.icon-pdf-load:before {
  content: "\73";
}
.icon-chart-dot:before {
  content: "\74";
}
.icon-invoice:before {
  content: "\75";
}
.icon-fisso-blu:before {
  content: "\76";
}
.icon-luce-blu:before {
  content: "\77";
}
.icon-internet-blu:before {
  content: "\78";
}
.icon-gas-blu:before {
  content: "\79";
}
