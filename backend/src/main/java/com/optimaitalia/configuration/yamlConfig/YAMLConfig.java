package com.optimaitalia.configuration.yamlConfig;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;


@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "restdata")
public class YAMLConfig {
    private String token;
    private HashMap<String, String> urls;

    public HashMap<String, String> getUrls() {
        return urls;
    }

    public void setUrls(HashMap<String, String> urls) {
        this.urls = urls;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}