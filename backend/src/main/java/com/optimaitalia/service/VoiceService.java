package com.optimaitalia.service;

import com.optimaitalia.model.wrappers.voice.VoiceClientCli;
import com.optimaitalia.model.wrappers.voip.SchedaTecnicaVoce;

import java.util.List;

public interface VoiceService {

    List<VoiceClientCli> findVoiceClientCli(Long clientId);

    List<VoiceClientCli> findVoiceClientCli(Long clientId, String pod);
    SchedaTecnicaVoce getVoipInfo(String dialer);
    List<VoiceClientCli> getVoiceClientCliWithVlanId(Long clientId);

}
