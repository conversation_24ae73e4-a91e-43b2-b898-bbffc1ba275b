package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.mobile.conracts.ContractRecord;
import com.optimaitalia.model.wrappers.paypal.PayPalActivation;
import com.optimaitalia.model.wrappers.paypal.PayPalActivationDilazione;
import com.optimaitalia.model.wrappers.paypal.PayPalActivationRicarica;
import com.optimaitalia.service.MobileService;
import com.optimaitalia.service.PayPalService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Service
public class PayPalServiceImpl implements PayPalService {


    private final SecurityService securityService;
    private final RestTemplate restTemplate;
    private final MobileService mobileService;

    @Value("${paypal.url.ko}")
    private String paypalUrlKo;

    @Value("${paypal.url.ok}")
    private String paypalUrlOk;

    @Value("${restdata.urls.payPalActivation}")
    private String payPalActivationUrl;

    public PayPalServiceImpl(RestTemplate restTemplate, SecurityService securityService, MobileService mobileService) {
        this.securityService = securityService;
        this.restTemplate = restTemplate;
        this.mobileService = mobileService;
    }

    @Override
    public ResponseEntity postPayPalActivation(PayPalActivation body) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        body.setURL_KO(paypalUrlKo);
        body.setURL_OK(paypalUrlOk);
        HttpEntity<?> httpEntity = new HttpEntity<>(body, httpHeaders);
        Map response = restTemplate.exchange(payPalActivationUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        return new ResponseEntity(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity postPayPalActivationRicarica(PayPalActivationRicarica body) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        body.setURL_KO(paypalUrlKo);
        body.setURL_OK(paypalUrlOk);
        HttpEntity<?> httpEntity = new HttpEntity<>(body, httpHeaders);
        Map response = restTemplate.exchange(payPalActivationUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        return new ResponseEntity(response, HttpStatus.OK);
    }

    @Override
    public ResponseEntity postPayPalActivationDilazione(PayPalActivationDilazione body) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        body.setURL_KO(paypalUrlKo);
        body.setURL_OK(paypalUrlOk);
        HttpEntity<?> httpEntity = new HttpEntity<>(body, httpHeaders);
        Map response = restTemplate.exchange(payPalActivationUrl, HttpMethod.POST, httpEntity, Map.class).getBody();
        return new ResponseEntity(response, HttpStatus.OK);
    }

    @Override
    public Boolean isOptimaNumber(Long simNumber) {
        List<ContractRecord> mobileContracts = mobileService.findMobileContracts(null, simNumber);
        return mobileContracts.isEmpty();
    }
}
