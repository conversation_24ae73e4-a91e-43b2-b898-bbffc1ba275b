package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.adsl.PodDetail;
import com.optimaitalia.service.AdslService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AdslServiceImpl implements AdslService {

    private static final Logger logger = LogManager.getLogger(AdslServiceImpl.class);

    private final RestTemplate sslRestTemplate;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    @Value("${restdata.urls.adsl-client-cli}")
    private String adslClientCliUrl;

    public AdslServiceImpl(RestTemplate sslRestTemplate, ObjectMapper objectMapper, SecurityService securityService) {
        this.sslRestTemplate = sslRestTemplate;
        this.objectMapper = objectMapper;
        this.securityService = securityService;
    }

    @Override
    public List<PodDetail> findAdslPodDetails(Long clientId) {
        logger.info("Obtaining list of linea details for ads service for client with id {}", clientId);
        if (clientId != null) {
            HttpHeaders httpHeaders = new HttpHeaders();
            //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(httpHeaders);
            Map response = sslRestTemplate.exchange(this.adslClientCliUrl, HttpMethod.GET, httpEntity, Map.class, clientId).getBody();
            logger.info("linea details have been updated.");
            if (response != null && response.get("LineeCliente") != null) {
                return objectMapper.convertValue(((Map) response.get("LineeCliente")).get("LineeADSL"),
                        objectMapper.getTypeFactory().constructCollectionType(List.class, PodDetail.class));
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<PodDetail> findAdslPodDetails(Long clientId, String pod) {
        return this.findAdslPodDetails(clientId).stream().filter(i -> pod.equals(i.getNumeroDiAppoggio()) ||
                pod.equals(i.getNumeroRisorsa())).collect(Collectors.toList());
    }
}
