package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.routerInfo.RouterInfo;
import com.optimaitalia.model.routerInfo.SchedaTecnica;
import com.optimaitalia.model.routerInfo.UserParametri;
import com.optimaitalia.service.RouterService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class RouterServiceImpl implements RouterService {
    private @Value("${restdata.urls.router}")
    String routerUrl;
    private @Value("${restdata.urls.schedaTecnicaLinea}")
    String schedaTecnicaLineaUrl;
    private final RestTemplate restTemplate;
    private final SecurityService securityService;
    private final ObjectMapper objectMapper;

    public RouterServiceImpl(RestTemplate restTemplate, SecurityService securityService, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.securityService = securityService;
        this.objectMapper = objectMapper;
    }

    @Override
    public RouterInfo getRouterInfo(Long clientId) {
        if (clientId != null) {
            HttpHeaders httpHeaders = new HttpHeaders();
            //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());

            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(httpHeaders);
            try {
                ResponseEntity<Map> exchange = restTemplate.exchange(routerUrl, HttpMethod.GET, httpEntity, Map.class, clientId);
                Map<String, Object> response = (Map<String, Object>) exchange.getBody().get("LineeCliente");

                RouterInfo routerInfo = objectMapper.convertValue(response, RouterInfo.class);
                routerInfo.getLineeADSL().forEach(item -> {
                    UserParametri userParametri = getUserParametri(item.getIdLinea());
                    if (userParametri != null) {
                        item.setUesrname(userParametri.getParametri().getUesrname());
                        item.setPsw(userParametri.getParametri().getPsw());
                        item.setPhoneNumber(userParametri.getParametri().getPhoneNumber());
                    }
                });
                return routerInfo;
            } catch (HttpServerErrorException e) {
                return null;
            }
        }
        return null;
    }

    private UserParametri getUserParametri(String id) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(httpHeaders);
        ResponseEntity<Map> exchange = restTemplate.exchange(schedaTecnicaLineaUrl, HttpMethod.GET, httpEntity, Map.class, id);
        Map<String, Object> response = (Map<String, Object>) exchange.getBody().get("response");
        return objectMapper.convertValue(response, SchedaTecnica.class).getSchedaTecnica();
    }
}
// LineeADSL