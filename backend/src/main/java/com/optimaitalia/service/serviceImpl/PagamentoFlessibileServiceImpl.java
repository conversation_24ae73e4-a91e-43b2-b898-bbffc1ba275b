package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optimaitalia.service.PagamentoFlessibileService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class PagamentoFlessibileServiceImpl implements PagamentoFlessibileService {

    private final ObjectMapper objectMapper;

    private final RestTemplate restTemplate;

    @Value("${restdata.urls.pagamentoFlessibile}")
    private String pagamentoFlessibileUrl;


    public PagamentoFlessibileServiceImpl(ObjectMapper objectMapper, RestTemplate restTemplate) {
        this.objectMapper = objectMapper;
        this.restTemplate = restTemplate;
    }

    @Override
    public Boolean isClienteEligibile(String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> body = new HashMap<>();
        body.put("cliente", clientId);

        HttpEntity<Map> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.pagamentoFlessibileUrl, HttpMethod.GET, httpEntity,
                Map.class, clientId);
        Map<String, Object> responseMap = (Map<String, Object>) exchange.getBody();

        return (Boolean) responseMap.get("clienteEligibile");
    }
}
