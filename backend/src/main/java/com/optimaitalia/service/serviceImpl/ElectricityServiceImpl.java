package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.charts.ChartInformation;
import com.optimaitalia.model.charts.ListaConsumiDettagliOre;
import com.optimaitalia.model.wrappers.services.EnergyDetailsByHoursRequest;
import com.optimaitalia.model.wrappers.services.InfoPod2G;
import com.optimaitalia.model.wrappers.services.PodDetail;
import com.optimaitalia.model.wrappers.services.PodDetailsRequest;
import com.optimaitalia.service.ElectricityService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ElectricityServiceImpl implements ElectricityService {

    private static final Logger logger = LogManager.getLogger(ElectricityServiceImpl.class);

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    private final OvalValidator ovalValidator;

    @Value("${restdata.urls.electricity-pod-details}")
    private String electricityPodDetailsUrl;

    @Value("${restdata.urls.electricity-autolettura-2g}")
    private String electricityAutolettura2G;

    @Value("${restdata.urls.electricity-point-adjustments}")
    private String energyPointAdjustmentsUrl;

    @Value("${restdata.urls.electricity-details-by-hours}")
    private String energyDetailsByHoursUrl;

    public ElectricityServiceImpl(RestTemplate restTemplate, ObjectMapper objectMapper,
                                  SecurityService securityService, OvalValidator ovalValidator) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.securityService = securityService;
        this.ovalValidator = ovalValidator;
    }

    @Override
    public List<PodDetail> findPodDetails(PodDetailsRequest podDetailsRequest) throws ValidateException {
        ovalValidator.validate(podDetailsRequest);
        logger.info("Obtaining list of pod details by pod requests: {}", podDetailsRequest.getPodRequests());
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<PodDetailsRequest> httpEntity = new HttpEntity<>(podDetailsRequest, httpHeaders);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.electricityPodDetailsUrl, HttpMethod.POST, httpEntity, Map.class);
        logger.debug("Pod details has been obtained.");
        return objectMapper.convertValue(exchange.getBody().get("listaPod"), objectMapper.getTypeFactory().constructCollectionType(List.class, PodDetail.class));
    }

    @Override
    public List<InfoPod2G> getPod2GInfo(String[] podDetailsRequest) {
        logger.info("Obtaining list of pod details about 2G");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("ListaPod", podDetailsRequest);
        HttpEntity<Map> httpEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<Map> exchange = this.restTemplate.exchange(this.electricityAutolettura2G, HttpMethod.POST, httpEntity, Map.class);
        return objectMapper.convertValue(exchange.getBody().get("ListaInfoPod"), objectMapper.getTypeFactory().constructCollectionType(List.class, InfoPod2G.class));
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map> energyPointAdjustments(String clientId, Integer punto) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("cliente", clientId);
        body.put("fatturato", true);
        body.put("punto", punto);
        HttpEntity<Map> httpEntity = new HttpEntity<>(body, httpHeaders);
        return (List<Map>) this.restTemplate.exchange(this.energyPointAdjustmentsUrl, HttpMethod.POST, httpEntity,
                Map.class).getBody().get("listaConsumi");
    }

    @Override
    public List<ListaConsumiDettagliOre> energyDetailsByHours(EnergyDetailsByHoursRequest body) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<?> httpEntity = new HttpEntity<>(body, httpHeaders);
        return restTemplate.exchange(energyDetailsByHoursUrl, HttpMethod.POST, httpEntity, ChartInformation.class).getBody().getListaConsumiDettagliOre();
    }

    @Override
    public byte[] getFile(EnergyDetailsByHoursRequest detailsByHoursRequest) throws IOException {
        List<ListaConsumiDettagliOre> response = energyDetailsByHours(detailsByHoursRequest);
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Report");

        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Times New Roman");
        font.setBold(true);
        font.setFontHeight((short) 230);
        headerStyle.setFont(font);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 24));
        XSSFRow firstHeader = sheet.createRow(0);
        firstHeader.createCell(1).setCellValue("Consumi in kWh per ora");
        firstHeader.getCell(1).setCellStyle(headerStyle);

        XSSFRow secondHeader = sheet.createRow(1);
        String[] headerCells = {"Data Consumo", "h 1:00", "h 2:00", "h 3:00", "h 4:00", "h 5:00",
                "h 6:00", "h 7:00", "h 8:00", "h 9:00", "h 10:00", "h 11:00", "h 12:00",
                "h 13:00", "h 14:00", "h 15:00", "h 16:00", "h 17:00", "h 18:00", "h 19:00",
                "h 20:00", "h 21:00", "h 22:00", "h 23:00", "h 24:00"};
        for (int i = 0; i < headerCells.length; i++) {
            secondHeader.createCell(i).setCellValue(headerCells[i]);
            sheet.autoSizeColumn(i);
            secondHeader.getCell(i).setCellStyle(headerStyle);
        }
        if (response != null && !response.isEmpty()) {
            for (int i = 0; i < response.size(); i++) {
                XSSFRow body = sheet.createRow(i + 2);
                body.createCell(0).setCellValue(response.get(i).getDataConsumo());
                body.createCell(1).setCellValue(response.get(i).getOra1());
                body.createCell(2).setCellValue(response.get(i).getOra2());
                body.createCell(3).setCellValue(response.get(i).getOra3());
                body.createCell(4).setCellValue(response.get(i).getOra4());
                body.createCell(5).setCellValue(response.get(i).getOra5());
                body.createCell(6).setCellValue(response.get(i).getOra6());
                body.createCell(7).setCellValue(response.get(i).getOra7());
                body.createCell(8).setCellValue(response.get(i).getOra8());
                body.createCell(9).setCellValue(response.get(i).getOra9());
                body.createCell(10).setCellValue(response.get(i).getOra10());
                body.createCell(11).setCellValue(response.get(i).getOra11());
                body.createCell(12).setCellValue(response.get(i).getOra12());
                body.createCell(13).setCellValue(response.get(i).getOra13());
                body.createCell(14).setCellValue(response.get(i).getOra14());
                body.createCell(15).setCellValue(response.get(i).getOra15());
                body.createCell(16).setCellValue(response.get(i).getOra16());
                body.createCell(17).setCellValue(response.get(i).getOra17());
                body.createCell(18).setCellValue(response.get(i).getOra18());
                body.createCell(19).setCellValue(response.get(i).getOra19());
                body.createCell(20).setCellValue(response.get(i).getOra20());
                body.createCell(21).setCellValue(response.get(i).getOra21());
                body.createCell(22).setCellValue(response.get(i).getOra22());
                body.createCell(23).setCellValue(response.get(i).getOra23());
                body.createCell(24).setCellValue(response.get(i).getOra24());
            }
        } else {
            XSSFRow body = sheet.createRow(1);
            body.createCell(0).setCellValue("Data Not Found");
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            workbook.write(bos);
            return bos.toByteArray();
        }
    }
}
