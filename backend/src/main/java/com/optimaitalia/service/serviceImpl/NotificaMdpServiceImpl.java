package com.optimaitalia.service.serviceImpl;

import com.optimaitalia.model.db.NotificaMdp;
import com.optimaitalia.repository.NotificaMdpRepository;
import com.optimaitalia.service.NotificaMdpService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class NotificaMdpServiceImpl implements NotificaMdpService {

    private static final Logger logger = LogManager.getLogger(NotificaMdpServiceImpl.class);

    private final NotificaMdpRepository notificaMdpRepository;

    public NotificaMdpServiceImpl(NotificaMdpRepository notificaMdpRepository) {
        this.notificaMdpRepository = notificaMdpRepository;
    }

    @Override
    public List<NotificaMdp> getNotificationMdpInfo(Long clientId) {
        logger.info("Initialized NotificaMdpService for user with id {}", clientId);
        List<NotificaMdp> resultList = new ArrayList<>();
        if (clientId != null) {
            List<NotificaMdp> mdp1 = notificaMdpRepository.findAllById(clientId);
            for (NotificaMdp mdp : mdp1) {
                if (!mdp.getIsDisplayedNotificationText()) {
                    logger.info("Obtaining notification for user with id {}", clientId);
                    mdp.setIsDisplayedNotificationText(true);
                    notificaMdpRepository.save(mdp);
                    if (mdp.getNotificationText() != null && mdp.getTextMarker() != null) {
                        getNotificationTextPartsWithMarker(mdp);
                    }
                    resultList.add(mdp);
                }
            }
            return resultList;
        }
        logger.info("No users have been found.");
        return resultList;
    }

    @Override
    public void save(NotificaMdp notificaMdp) {
       if (notificaMdp != null) {
           notificaMdp.setIsDisplayedNotificationText(false);
           notificaMdpRepository.save(notificaMdp);
       }
    }

    private void getNotificationTextPartsWithMarker(NotificaMdp notificaMdp) {
        String notificationText = notificaMdp.getNotificationText();
        String marker = notificaMdp.getTextMarker();
        String firstPartText;
        String secondPartText = "";
        if (notificationText.split(marker).length > 1) {
            firstPartText = notificationText.split(marker)[0];
            secondPartText = notificationText.split(marker)[1];
        } else firstPartText = notificationText.split(marker)[0];
        notificaMdp.setNotificationTextFirstPart(firstPartText);
        notificaMdp.setNotificationTextSecondPart(secondPartText);
    }
}
