package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.model.userData.UserData;
import com.optima.security.service.SecurityService;
import com.optimaitalia.model.wrappers.communication.CommunicationNote;
import com.optimaitalia.model.wrappers.communication.CustomerEmailInfo;
import com.optimaitalia.model.wrappers.communication.RecommendedBlock;
import com.optimaitalia.service.CommunicationService;
import com.optimaitalia.service.InformationService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CommunicationServiceImpl implements CommunicationService {

    private final RestTemplate restTemplate;

    private final InformationService informationService;

    private final SecurityService securityService;

    private final ObjectMapper objectMapper;
    @Value("${restdata.urls.communication-email-info}")
    private String communicationEmailInfoUrl;

    @Value("${restdata.urls.recommendations-blocks}")

    private String recommendationBlocksUrl;
    @Value("${restdata.urls.сomunicazioniNote}")
    private String communicationNoteUrl;

    @Value("${restdata.urls.getPDF}")
    private String pdfDownloadUrl;

    public CommunicationServiceImpl(RestTemplate restTemplate, InformationService informationService, SecurityService securityService, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.informationService = informationService;
        this.securityService = securityService;
        this.objectMapper = objectMapper;
    }


    @Override
    @SuppressWarnings("uncheked")
    public List<CustomerEmailInfo> getCustomerCommunicationEmailInfo(String clientId) {
        UserData userData = informationService.getUserData(clientId);
        /*
        if (userData != null && userData.getAccountId() != null) {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
            URI uri = UriComponentsBuilder.fromHttpUrl(this.communicationEmailInfoUrl).path(userData.getAccountId())
                    .queryParam("offset", 0)
                    .queryParam("rows", 26)
                    .queryParam("order", "null")
                    .queryParam("direction", "asc")
                    .queryParam("filtro", "TipoImpegno=Email~Sender=<EMAIL>")
                    .build().toUri();
            HttpEntity httpEntity = new HttpEntity(httpHeaders);
            Map exchange = restTemplate.exchange(uri, HttpMethod.GET, httpEntity, Map.class).getBody();
            if (exchange != null && exchange.get("response") != null) {
                Map<String, Object> response = (Map<String, Object>) exchange.get("response");
                return objectMapper.convertValue(response.get("Content"), objectMapper.getTypeFactory()
                        .constructCollectionType(List.class, CustomerEmailInfo.class));
            }
        }*/
        return new ArrayList<>();
    }

    @Override
    public List<RecommendedBlock> loadCustomerRecommendationBlocks(String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity httpEntity = new HttpEntity(httpHeaders);
        Map body = restTemplate.exchange(this.recommendationBlocksUrl, HttpMethod.GET, httpEntity, Map.class, clientId).getBody();
        if (body != null) {
            return objectMapper.convertValue(body.get("raccomandate"), objectMapper.getTypeFactory()
                    .constructCollectionType(List.class, RecommendedBlock.class));
        }
        return new ArrayList<>();
    }

    @Override
    public ResponseEntity<byte[]> downloadRecommendedFile(String clientId, String fileName) {
        if (!StringUtils.isEmpty(clientId) && !StringUtils.isEmpty(fileName)) {
            List<RecommendedBlock> recommendedBlocks = this.loadCustomerRecommendationBlocks(clientId);
            Optional<RecommendedBlock> recommendedBlock = recommendedBlocks.stream()
                    .filter(block -> block.getRecommendedUrl().contains(fileName)).findFirst();
            if (recommendedBlock.isPresent()) {
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
                HttpEntity httpEntity = new HttpEntity(httpHeaders);
                ResponseEntity<byte[]> exchange = restTemplate.exchange(this.pdfDownloadUrl + recommendedBlock.get().getPdfName(),
                        HttpMethod.GET, httpEntity, byte[].class);
                if (exchange.hasBody()) {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentDisposition(exchange.getHeaders().getContentDisposition());
                    headers.setContentType(MediaType.APPLICATION_PDF);
                    return ResponseEntity.ok().headers(headers).body(exchange.getBody());
                }
            }
        }
        return ResponseEntity.notFound().build();
    }

    @Override
    public ResponseEntity<byte[]> downloadCommuniationNoteFile(String clientId, String fileName) {
        List<CommunicationNote> noteList = getCommunicationNote(clientId).stream().filter(elem -> elem != null && elem.getAllegati().size() > 0 && elem.getAllegati().stream().anyMatch(allegati -> allegati != null && allegati.getUri() != null && allegati.getUri().equals(fileName))
        ).collect(Collectors.toList());
        if (noteList.size() > 0) {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_OCTET_STREAM));
            HttpEntity httpEntity = new HttpEntity(httpHeaders);
            ResponseEntity<byte[]> exchange = restTemplate.exchange(this.pdfDownloadUrl + fileName,
                    HttpMethod.GET, httpEntity, byte[].class);
            if (exchange.hasBody()) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_PDF);
                //headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"));
                headers.setContentDisposition(exchange.getHeaders().getContentDisposition());
                return ResponseEntity.ok().headers(headers).body(exchange.getBody());
            }
        }
        return ResponseEntity.notFound().build();
    }

    @Override
    public List<CommunicationNote> getCommunicationNote(String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity httpEntity = new HttpEntity(httpHeaders);
        Map body = restTemplate.exchange(this.communicationNoteUrl, HttpMethod.GET, httpEntity, Map.class, clientId).getBody();
        if (body != null) {
            Map response = (Map) body.get("response");
            if (response != null) {
                return objectMapper.convertValue(response.get("Content"), objectMapper.getTypeFactory()
                        .constructCollectionType(List.class, CommunicationNote.class));
            }
        }
        return new ArrayList<>();
    }
}
