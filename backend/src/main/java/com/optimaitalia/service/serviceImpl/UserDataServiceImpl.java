package com.optimaitalia.service.serviceImpl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.common.exceptions.ValidateException;
import com.optima.common.validators.OvalValidator;
import com.optima.security.repository.dao.UserDao;
import com.optima.security.repository.dao.impl.UserDaoImpl;
import com.optima.security.service.SecurityService;
import com.optimaitalia.builders.UserDataChangeBuilder;
import com.optimaitalia.model.condominio.Condominio;
import com.optimaitalia.model.enums.IncidentEvent;
import com.optimaitalia.model.enums.IncidentEventAnnotation;
import com.optimaitalia.model.enums.IncidentEventCategory;
import com.optimaitalia.model.enums.ServiceType;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventRequest;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.model.wrappers.user.requests.*;
import com.optimaitalia.model.wrappers.user.response.ChangePersonalDataResponse;
import com.optimaitalia.service.IncidentEventService;
import com.optimaitalia.service.UserDataService;
import com.optimaitalia.utils.ServiceExecutionStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UserDataServiceImpl implements UserDataService {

    private static final Logger logger = LogManager.getLogger(UserDataServiceImpl.class);

    private final IncidentEventService incidentEventService;

    private final UserDataChangeBuilder<UserDataChangeRequest, Change> userDataChangeBuilder;

    private final SecurityService securityService;

    private final RestTemplate restTemplate;

    private final OvalValidator ovalValidator;

    private final ObjectMapper objectMapper;

    private final UserDao userDao;

    @Value("${restdata.urls.condominio}")
    private String condominioInfoUrl;

    @Value("${restdata.urls.сondominioFiscalCode}")
    private String condominioFiscalCodeInfoUrl;

    @Value("${user.change.key}")
    private String userKey;

    @Value("${user.change.change-personal-data-url}")
    private String modifyPersonalDataUrl;

    @Value("${user.change.change-password-identification-url}")
    private String modifyPasswordIdentificationUrl;

    @Value("${user.change.change-shipping-type-url}")
    private String changeShipmentMethodUrl;

    @Value("${user.change.origin}")
    private String origin;

    @Value("${mobile.service.url.user-geolocation}")
    private String userGeolocation;

    private Map<IncidentEventCategory, String> annotationsMap;

    private Map<IncidentEventCategory, String> userDataServiceAddressMap;

    {
        annotationsMap = new HashMap<>();
        annotationsMap.put(IncidentEventCategory.CHANGE_SOCIAL_NAME, "Variazione ragione sociale da %s. Marco a %s");
        annotationsMap.put(IncidentEventCategory.CHANGE_INVOICE_SHIPPING_METHOD, "Variazione Modalità di ricezione fattura da %s a %s");
        annotationsMap.put(IncidentEventCategory.CHANGE_TELEPHONE_NUMBER, "Variazione Numeri di riferimento da %s a %s");
        annotationsMap.put(IncidentEventCategory.CHANGE_EMAIL, "Variazione E-mail da %s a %s");
        annotationsMap.put(IncidentEventCategory.CHANGE_BILLING_ADDRESS, "Richiesta di modifica Indirizzo di fatturazione da %s a %s.");
        annotationsMap.put(IncidentEventCategory.CHANGE_OFFICE_ADDRESS, "Richiesta di modifica Sede legale da %s a %s.");

        annotationsMap.put(IncidentEventCategory.PEC_VARIATION, "Richiesta indirizzo PEC da %s a %s.");
        annotationsMap.put(IncidentEventCategory.CHANGE_RECIPIENT_CODE, "Richiesta codice destinatario da %s a %s.");
        annotationsMap.put(IncidentEventCategory.POST_ATTIVAZIONE, "%s");

    }

    public UserDataServiceImpl(IncidentEventService incidentEventService,
                               UserDataChangeBuilder<UserDataChangeRequest, Change> userDataChangeBuilder,
                               SecurityService securityService,
                               RestTemplate restTemplate, OvalValidator ovalValidator,
                               Environment environment, ObjectMapper objectMapper, UserDaoImpl userDao) {
        this.incidentEventService = incidentEventService;
        this.userDataChangeBuilder = userDataChangeBuilder;
        this.securityService = securityService;
        this.restTemplate = restTemplate;
        this.ovalValidator = ovalValidator;
        this.objectMapper = objectMapper;
        this.userDao = userDao;
        userDataServiceAddressMap = new HashMap<>();
        userDataServiceAddressMap.put(IncidentEventCategory.CHANGE_RECIPIENT_CODE, environment.getProperty("user.change.recipient-code-change-url"));
    }

    @Override
    public ChangePersonalDataResponse changePersonalUserData(PersonalDataChangeRequest userDataChangeRequest) throws ValidateException {
        ovalValidator.validate(userDataChangeRequest);
        HttpHeaders headers = new HttpHeaders();
        //headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        ChangeRequest changeRequest = this.buildRequest(userDataChangeRequest);
        if (changeRequest != null) {
            HttpEntity<ChangeRequest> httpEntity = new HttpEntity<>(changeRequest, headers);
            if (userDataChangeRequest.getIncidentEventCategory().equals(IncidentEventCategory.CHANGE_INVOICE_SHIPPING_METHOD)) {
                return restTemplate.postForEntity(changeShipmentMethodUrl, httpEntity, ChangePersonalDataResponse.class).getBody();
            }
            if (userDataChangeRequest.getIncidentEventCategory().equals(IncidentEventCategory.CHANGE_EMAIL)) {
                userDao.updateEmail(String.valueOf(userDataChangeRequest.getClientId()), userDataChangeRequest.getValue());
            }
            String address = userDataServiceAddressMap.get(userDataChangeRequest.getIncidentEventCategory());
            return restTemplate.postForEntity(!StringUtils.isEmpty(address) ? address : modifyPersonalDataUrl,
                    httpEntity, ChangePersonalDataResponse.class).getBody();
        }
        return new ChangePersonalDataResponse();
    }

    @Override
    public IncidentEventResponse changeBillingAddress(ChangeAddressRequest request) throws ValidateException {
        ovalValidator.validate(request);
        IncidentEventRequest incidentEventRequest = new IncidentEventRequest();
        incidentEventRequest.setCustomerId(request.getClientId());
        incidentEventRequest.setIncidentEvent(IncidentEvent.PERSONAL_DATA_CHANGE);
        incidentEventRequest.setIncidentCategory(request.getIncidentEventCategory());
        incidentEventRequest.setServiceType(ServiceType.CHANGE_USER_DATA.getValue().toString());
        String annotation = annotationsMap.get(request.getIncidentEventCategory());
        if (!StringUtils.isEmpty(annotation)) {
            incidentEventRequest.setIncidentAnnotation(String.format(annotation, request.getAddressToChange(),
                    request.getNewAddress()));
        }
        if (incidentEventRequest.getIncidentAnnotation() != null) {
            incidentEventRequest.setOrigin(origin);
            return incidentEventService.customerIncidentEvent(incidentEventRequest);
        }
        return new IncidentEventResponse();
    }

    private ChangeRequest buildRequest(PersonalDataChangeRequest userDataChangeRequest) throws ValidateException {
        IncidentEventRequest incidentEventRequest = new IncidentEventRequest();
        incidentEventRequest.setCustomerId(userDataChangeRequest.getClientId());
        incidentEventRequest.setIncidentEvent(IncidentEvent.PERSONAL_DATA_CHANGE);
        incidentEventRequest.setIncidentCategory(userDataChangeRequest.getIncidentEventCategory());
        incidentEventRequest.setServiceType(ServiceType.CHANGE_USER_DATA.getValue().toString());
        incidentEventRequest.setIncidentAnnotation(IncidentEventAnnotation.REQUEST_FOR_PERSONAL_CHANGES.getValue());
        incidentEventRequest.setOrigin(origin);
        String annotation = annotationsMap.get(userDataChangeRequest.getIncidentEventCategory());
        if (!StringUtils.isEmpty(annotation)) {
            incidentEventRequest.setIncidentAnnotation(String.format(annotation, userDataChangeRequest.getOldValue(), userDataChangeRequest.getValue()));
        }
        IncidentEventResponse incidentEventResponse = incidentEventService.customerIncidentEvent(incidentEventRequest);
        if (ServiceExecutionStatus.OK.name().equals(incidentEventResponse.getStatus()) && !StringUtils.isEmpty(incidentEventResponse.getIncidentId())) {
            ChangeRequest changeRequest = new ChangeRequest();
            changeRequest.setCustomerId(String.valueOf(userDataChangeRequest.getClientId()));
            changeRequest.setUser(userKey);
            changeRequest.setUserFullName("Variazione da selfcare");
            changeRequest.setIncidentId(incidentEventResponse.getIncidentId());
            changeRequest.setChange(userDataChangeBuilder.build(userDataChangeRequest));
            return changeRequest;
        }
        return null;
    }

    @Override
    public ChangePersonalDataResponse changePasswordIdentification(ChangePasswordIdentificationRequest userDataChangeRequest) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        logger.info("Updating password identification for user with id {}", userDataChangeRequest.getCustomerId());
        HttpEntity<?> httpEntity = new HttpEntity<>(userDataChangeRequest, httpHeaders);
        return restTemplate.exchange(modifyPasswordIdentificationUrl, HttpMethod.POST, httpEntity, ChangePersonalDataResponse.class).getBody();
    }

    @Override
    public List<Condominio> getUserCondominioInfo(String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<?> httpEntity = new HttpEntity<>(httpHeaders);
        Map body = restTemplate.exchange(this.condominioInfoUrl, HttpMethod.GET, httpEntity, Map.class, clientId).getBody();
        if (body != null) {
            List<Condominio> response = objectMapper.convertValue(body.get("response"),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Condominio.class));
            logger.info("Getting the List<Condominio> from CondominioInfo for user with id {}", clientId);
            return response;
        }
        return new ArrayList<>();
    }

    @Override
    public ResponseEntity<?> getUserCondominioFiscalCode(String clientId) {
        HttpHeaders headers = new HttpHeaders();
        //headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("@IdCliente", clientId);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(body, headers);
        return new ResponseEntity<>(restTemplate.postForObject(this.condominioFiscalCodeInfoUrl, httpEntity, Map.class), HttpStatus.OK);
    }

    @Override
    public Geolocation getGeolocationInfo() {
        HttpHeaders httpHeaders = new HttpHeaders();
        //httpHeaders.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<?> httpEntity = new HttpEntity<>(httpHeaders);
        Map body = restTemplate.exchange(this.userGeolocation, HttpMethod.GET, httpEntity, Map.class).getBody();
        if (body != null) return objectMapper.convertValue(body, Geolocation.class);
        return new Geolocation();
    }

}
