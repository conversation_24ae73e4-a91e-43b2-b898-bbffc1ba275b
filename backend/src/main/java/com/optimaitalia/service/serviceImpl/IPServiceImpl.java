package com.optimaitalia.service.serviceImpl;

import com.optima.security.service.impl.OTPServiceImpl;
import com.optimaitalia.model.IPInfo;
import com.optimaitalia.service.IPService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class IPServiceImpl implements IPService {

    private static final Logger logger = LogManager.getLogger(IPServiceImpl.class);

    private final RestTemplate restTemplate;

    public IPServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public ResponseEntity<IPInfo> getIPInfo() {
        ResponseEntity<IPInfo> response = restTemplate.exchange("https://ipinfo.io/json", HttpMethod.GET, null, IPInfo.class);
        logger.info("IP info is obtained");
        return response;
    }
}
