package com.optimaitalia.controller;

import com.optimaitalia.service.PagamentoFlessibileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
public class PagamentoFlessibileController {

    private final PagamentoFlessibileService pagamentoFlessibileService;

    @Autowired
    public PagamentoFlessibileController(PagamentoFlessibileService pagamentoFlessibileService) {
        this.pagamentoFlessibileService = pagamentoFlessibileService;
    }


    @GetMapping("/pagamento-flessibile/{clientId}")
    @PreAuthorize("#clientId==authentication.principal.uid")
    public Boolean getDiazolineClienteDetail(@PathVariable("clientId") String clientId) {
        return pagamentoFlessibileService.isClienteEligibile(clientId);
    }
}
