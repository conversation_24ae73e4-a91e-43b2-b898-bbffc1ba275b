package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.voice.VoiceClientCli;
import com.optimaitalia.service.VoiceService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/voice")
public class VoiceController {

    private final VoiceService voiceService;

    public VoiceController(VoiceService voiceService) {
        this.voiceService = voiceService;
    }

    @GetMapping("/client/cli/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<VoiceClientCli> getVoiceClientCli(@PathVariable Long clientId) {
        return voiceService.findVoiceClientCli(clientId);
    }

    @GetMapping("/client/cli/vlanId/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<VoiceClientCli> getVoiceClientCliWithVlanId(@PathVariable Long clientId) {
        return voiceService.getVoiceClientCliWithVlanId(clientId);
    }
    @GetMapping("/client/cli/{clientId}/{pod}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<VoiceClientCli> getVoiceClientCli(@PathVariable Long clientId, @PathVariable String pod) {
        return voiceService.findVoiceClientCli(clientId, pod);
    }
}
