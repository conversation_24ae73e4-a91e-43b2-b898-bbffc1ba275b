package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.communication.CommunicationNote;
import com.optimaitalia.model.wrappers.communication.CustomerEmailInfo;
import com.optimaitalia.model.wrappers.communication.RecommendedBlock;
import com.optimaitalia.service.CommunicationService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@RestController
@RequestMapping("/api/communication")
public class CommunicationController {

    private final CommunicationService communicationService;

    public CommunicationController(CommunicationService communicationService) {
        this.communicationService = communicationService;
    }

    @GetMapping("/email/details/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<CustomerEmailInfo> getCustomerCommunicationEmailInfo(@PathVariable String clientId) {
        return communicationService.getCustomerCommunicationEmailInfo(clientId);
    }

    @GetMapping("/recommendation/blocks/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<RecommendedBlock> getCustomerRecommendationBlocks(@PathVariable String clientId) {
        return communicationService.loadCustomerRecommendationBlocks(clientId);
    }

    @GetMapping("/notes/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public List<CommunicationNote> getCommunicationNote(@PathVariable String clientId) {
        return communicationService.getCommunicationNote(clientId);
    }

    @GetMapping("/note/pdf/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<byte[]> downloadCommuniationNoteFile(@PathVariable String clientId, @RequestParam String file) {
        return communicationService.downloadCommuniationNoteFile(clientId, file);
    }

    @GetMapping("/recommended/pdf/{clientId}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<byte[]> downloadRecommendedFile(@PathVariable String clientId, @RequestParam String file) {
        return communicationService.downloadRecommendedFile(clientId, file);
    }

}
