package com.optimaitalia.controller;

import com.optima.security.model.prospect.ForgotPasswordRequest;
import com.optima.security.model.prospect.ForgotPasswordResponse;
import com.optimaitalia.model.prospectUser.ProspectUserContractsResponse;
import com.optimaitalia.model.prospectUser.emailToSupport.ProspectUserEmailToSupport;
import com.optimaitalia.service.ProspectUserInformationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/prospect/information")
public class ProspectUserController {

    private final ProspectUserInformationService prospectUserInformationService;

    public ProspectUserController(ProspectUserInformationService prospectUserInformationService) {
        this.prospectUserInformationService = prospectUserInformationService;
    }

    @GetMapping("/contracts")
    public ProspectUserContractsResponse getContractsByCodiceFiscale(@RequestParam String codiceFiscale) {
        return prospectUserInformationService.getContractsByCodiceFiscale(codiceFiscale);
    }

    @PostMapping("/upload-file")
    public ResponseEntity<?> sendFile(@RequestPart("file") MultipartFile[] files,
                                      @RequestPart("contractId") String contractId) {
        return prospectUserInformationService.uploadFile(files, contractId);
    }

    @PostMapping(value = "/email/support")
    public Integer sendEmailToSupport(@RequestBody ProspectUserEmailToSupport emailBody) {
        return prospectUserInformationService.sendEmailToSupport(emailBody);
    }

    @PostMapping("/forgotPassword")
    public ForgotPasswordResponse restorePassword(@RequestBody ForgotPasswordRequest request) {
        return prospectUserInformationService.restorePassword(request);
    }
}

