package com.optimaitalia.controller;

import com.optimaitalia.model.wrappers.LoggerRequest;
import com.optimaitalia.model.wrappers.user.requests.Geolocation;
import com.optimaitalia.service.UserDataService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/logger")
public class LoggerController {

    private static final Logger logger = LogManager.getLogger(LoggerController.class);
    private final UserDataService userDataService;

    public LoggerController(UserDataService userDataService) {
        this.userDataService = userDataService;
    }

    @PostMapping
    @PreAuthorize("#logRequest.clientId == authentication.principal.uid")
    public void sendLogs(@RequestBody LoggerRequest logRequest) {

        String clientId = logRequest.getClientId();
        Geolocation geolocation = userDataService.getGeolocationInfo();

        logger.info(geolocation + " ; User: " + clientId);
        for (String log : logRequest.getLogList()) {
            logger.info("User: " + clientId + " " + log);
        }
    }

}
