package com.optimaitalia.controller;

import com.optima.android.model.CustomerFirebase;
import com.optima.android.service.CustomerFirebaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/nagios")
public class NagiosController {

    @Autowired
    private CustomerFirebaseService customerFirebaseService;

    @GetMapping(value = "/isAlive",produces= MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> getisAlive() {

        try {
            CustomerFirebase result = customerFirebaseService.getLastCustomerFirebaseByCustomerId("231833");

        }catch (Exception e){
            return new ResponseEntity<>("Critical - Errore accesso al db", HttpStatus.OK);
        }

        return new ResponseEntity<>("OK - Accesso al Database", HttpStatus.OK);
    }
}

