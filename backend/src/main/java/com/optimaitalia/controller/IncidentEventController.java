package com.optimaitalia.controller;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventRequest;
import com.optimaitalia.model.wrappers.incidentEvent.IncidentEventResponse;
import com.optimaitalia.service.IncidentEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@RestController()
@RequestMapping("/api")
public class IncidentEventController {

    private final IncidentEventService incidentEventService;

    @Autowired
    public IncidentEventController(IncidentEventService incidentEventService) {
        this.incidentEventService = incidentEventService;
    }

    @PostMapping("incident-event")
    @PreAuthorize("#incidentEventRequest.customerId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#incidentEventRequest.customerId, authentication.principal.uid)")
    public IncidentEventResponse incidentEvent(@RequestBody IncidentEventRequest incidentEventRequest) throws ValidateException {
        return incidentEventService.customerIncidentEvent(incidentEventRequest);
    }

}
