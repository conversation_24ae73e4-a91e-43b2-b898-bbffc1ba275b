package com.optimaitalia.exception;

import java.util.HashMap;
import java.util.Map;

public class TokenException extends Exception {

    private Map<String, String> errotMap = new HashMap<>();

    public TokenException() {
    }

    public TokenException(Map<String, String> errotMap) {
        this.errotMap = errotMap;
    }

    public TokenException(String message, Map<String, String> errotMap) {
        super(message);
        this.errotMap = errotMap;
    }

    public TokenException(String message, Throwable cause, Map<String, String> errotMap) {
        super(message, cause);
        this.errotMap = errotMap;
    }


    public Map<String, String> getErrotMap() {
        return errotMap;
    }

    public void setErrotMap(Map<String, String> errotMap) {
        this.errotMap = errotMap;
    }
}
