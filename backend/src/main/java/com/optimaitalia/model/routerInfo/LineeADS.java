package com.optimaitalia.model.routerInfo;

import com.fasterxml.jackson.annotation.JsonProperty;

public class LineeADS {

    private String tipoxDSL;

    private String numeroRisorsa;

    private String numeroDiAppoggio;

    private String idLinea;

    private String stato;

    @JsonProperty("username")
    private String username;

    @JsonProperty("psw")
    private String psw;

    @JsonProperty(" phoneNumber")
    private String phoneNumber;

    public String getUesrname() {
        return username;
    }

    public void setUesrname(String uesrname) {
        this.username = uesrname;
    }

    public String getPsw() {
        return psw;
    }

    public void setPsw(String psw) {
        this.psw = psw;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    @JsonProperty("stato")
    public String getStato() {
        return stato;
    }
    @JsonProperty("Stato")
    public void setStato(String stato) {
        this.stato = stato;
    }

    @JsonProperty("tipoxDSL")
    public String getTipoxDSL() {
        return tipoxDSL;
    }

    @JsonProperty("number")
    public String getNumeroRisorsa() {
        return numeroRisorsa;
    }

    @JsonProperty("NumeroRisorsa")
    public void setNumeroRisorsa(String numeroRisorsa) {
        this.numeroRisorsa = numeroRisorsa;
    }

    @JsonProperty("additionalNumber")
    public String getNumeroDiAppoggio() {
        return numeroDiAppoggio;
    }

    @JsonProperty("NumeroDiAppoggio")
    public void setNumeroDiAppoggio(String numeroDiAppoggio) {
        this.numeroDiAppoggio = numeroDiAppoggio;
    }

    @JsonProperty("id")
    public String getIdLinea() {
        return idLinea;
    }

    @JsonProperty("IdLinea")
    public void setIdLinea(String idLinea) {
        this.idLinea = idLinea;
    }

    @JsonProperty("TipoxDSL")
    public void setTipoxDSL(String tipoxDSL) {
        this.tipoxDSL = tipoxDSL;
    }
}