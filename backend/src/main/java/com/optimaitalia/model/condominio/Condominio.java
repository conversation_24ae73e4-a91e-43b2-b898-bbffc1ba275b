package com.optimaitalia.model.condominio;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Condominio {

    private Long customerId;
    private String name;
    private String fiscalInfo;
    private Boolean moroso;
    private String sottoTipoCluster;
    private LegalAddress legalAddress;

    @Override
    public String toString() {
        return "Condominio: {" +
                "customerId=" + customerId +
                ", name='" + name + '\'' +
                ", fiscalInfo='" + fiscalInfo + '\'' +
                ", moroso=" + moroso +
                ", sottoTipoCluster='" + sottoTipoCluster + '\'' +
                ", legalAddress=" + legalAddress +
                '}';
    }
}
