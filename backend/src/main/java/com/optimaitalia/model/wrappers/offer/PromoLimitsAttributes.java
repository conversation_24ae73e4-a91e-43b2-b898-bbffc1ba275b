package com.optimaitalia.model.wrappers.offer;

public class PromoLimitsAttributes {
    private String competenzaFattura;
    private String dataUltimaPerSelezionareXMeseDiSconto;
    private String dataLimiteInferiore;
    private String dataLimiteSuperiore;

    public String getCompetenzaFattura() {
        return competenzaFattura;
    }

    public void setCompetenzaFattura(String competenzaFattura) {
        this.competenzaFattura = competenzaFattura;
    }

    public String getDataUltimaPerSelezionareXMeseDiSconto() {
        return dataUltimaPerSelezionareXMeseDiSconto;
    }

    public void setDataUltimaPerSelezionareXMeseDiSconto(String dataUltimaPerSelezionareXMeseDiSconto) {
        this.dataUltimaPerSelezionareXMeseDiSconto = dataUltimaPerSelezionareXMeseDiSconto;
    }

    public String getDataLimiteInferiore() {
        return dataLimiteInferiore;
    }

    public void setDataLimiteInferiore(String dataLimiteInferiore) {
        this.dataLimiteInferiore = dataLimiteInferiore;
    }

    public String getDataLimiteSuperiore() {
        return dataLimiteSuperiore;
    }

    public void setDataLimiteSuperiore(String dataLimiteSuperiore) {
        this.dataLimiteSuperiore = dataLimiteSuperiore;
    }
}
