package com.optimaitalia.model.wrappers.segnalazione;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class SiganlPostModel {
    @JsonProperty("LeftValue")
    private String leftValue;
    @JsonProperty("Operator")
    private String operator;
    @JsonProperty("RightValue")
    private String rightValue;
    @JsonProperty("RightValues")
    private List<Object> rightValues;
    @JsonProperty("RightNumericValues")
    private List<Object> rightNumericValues;

    public SiganlPostModel(String leftValue, String operator, String rightValue) {
        this.leftValue = leftValue;
        this.operator = operator;
        this.rightValue = rightValue;
    }


    public SiganlPostModel(String leftValue, String operator, List<Object> rightValues, Boolean isNumeric) {
        this.leftValue = leftValue;
        this.operator = operator;
        if(!isNumeric) {
            this.rightValues = rightValues;
        }else{
            this.rightNumericValues = rightValues;
        }
    }

    public String getLeftValue() {
        return leftValue;
    }

    public void setLeftValue(String leftValue) {
        this.leftValue = leftValue;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRightValue() {
        return rightValue;
    }

    public void setRightValue(String rightValue) {
        this.rightValue = rightValue;
    }

    public List<Object> getRightValues() {
        return rightValues;
    }

    public void setRightValues(List<Object> rightValues) {
        this.rightValues = rightValues;
    }
}
