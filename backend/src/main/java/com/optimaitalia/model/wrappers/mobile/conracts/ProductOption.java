package com.optimaitalia.model.wrappers.mobile.conracts;

public class ProductOption {

    private Integer id;

    private Boolean activateOnDemand;

    private Option option;

    private Integer optionId;

    private Integer productId;

    private Boolean renewable;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getActivateOnDemand() {
        return activateOnDemand;
    }

    public void setActivateOnDemand(Boolean activateOnDemand) {
        this.activateOnDemand = activateOnDemand;
    }

    public Option getOption() {
        return option;
    }

    public void setOption(Option option) {
        this.option = option;
    }

    public Integer getOptionId() {
        return optionId;
    }

    public void setOptionId(Integer optionId) {
        this.optionId = optionId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Boolean getRenewable() {
        return renewable;
    }

    public void setRenewable(Boolean renewable) {
        this.renewable = renewable;
    }
}
