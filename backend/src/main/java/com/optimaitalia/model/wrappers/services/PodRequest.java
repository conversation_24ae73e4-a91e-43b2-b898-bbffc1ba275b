package com.optimaitalia.model.wrappers.services;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

public class PodRequest {

    @NotNull
    private Long clientId;

    @NotNull
    private String pod;

    @JsonProperty("cliente")
    public Long getClientId() {
        return clientId;
    }

    @JsonProperty("clientId")
    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getPod() {
        return pod;
    }

    public void setPod(String pod) {
        this.pod = pod;
    }
}
