package com.optimaitalia.model.wrappers.mobile;

import net.sf.oval.constraint.Length;
import net.sf.oval.constraint.NotNull;

public class ActivateOptionRequest {

    @NotNull
    private Long customerId;

    @NotNull
    @Length(min = 12, max = 12)
    private Long simNumber;

    @NotNull
    private Long newOptionId;

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getSimNumber() {
        return simNumber;
    }

    public void setSimNumber(Long simNumber) {
        this.simNumber = simNumber;
    }

    public Long getNewOptionId() {
        return newOptionId;
    }

    public void setNewOptionId(Long newOptionId) {
        this.newOptionId = newOptionId;
    }
}
