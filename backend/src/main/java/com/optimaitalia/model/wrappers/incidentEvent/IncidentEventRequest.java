package com.optimaitalia.model.wrappers.incidentEvent;

import com.optimaitalia.model.enums.IncidentEvent;
import com.optimaitalia.model.enums.IncidentEventCategory;
import net.sf.oval.constraint.NotNull;

import java.util.List;

public class IncidentEventRequest {

    @NotNull
    private Long customerId;

    private IncidentEvent incidentEvent;

    private IncidentEventCategory incidentCategory;

    private String serviceType;

    private String incidentAnnotation;

    private String origin;

    private List<GenericAttribute> genericAttributes;

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public IncidentEvent getIncidentEvent() {
        return incidentEvent;
    }

    public void setIncidentEvent(IncidentEvent incidentEvent) {
        this.incidentEvent = incidentEvent;
    }

    public IncidentEventCategory getIncidentCategory() {
        return incidentCategory;
    }

    public void setIncidentCategory(IncidentEventCategory incidentCategory) {
        this.incidentCategory = incidentCategory;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getIncidentAnnotation() {
        return incidentAnnotation;
    }

    public void setIncidentAnnotation(String incidentAnnotation) {
        this.incidentAnnotation = incidentAnnotation;
    }

    public List<GenericAttribute> getGenericAttributes() {
        return genericAttributes;
    }

    public void setGenericAttributes(List<GenericAttribute> genericAttributes) {
        this.genericAttributes = genericAttributes;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    @Override
    public String toString() {
        return "IncidentEventRequest{" +
                "customerId=" + customerId +
                ", incidentEvent=" + incidentEvent +
                ", incidentCategory=" + incidentCategory +
                ", serviceType='" + serviceType + '\'' +
                ", incidentAnnotation='" + incidentAnnotation + '\'' +
                ", origin='" + origin + '\'' +
                ", genericAttributes=" + genericAttributes +
                '}';
    }
}
