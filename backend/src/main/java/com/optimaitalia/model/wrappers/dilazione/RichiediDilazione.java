package com.optimaitalia.model.wrappers.dilazione;

import lombok.Data;

import java.util.List;

@Data
public class RichiediDilazione {

    private Boolean fattibilita;

    private Integer maxNumRate;

    private String maxDataPrimaRata;

    private List<Cadenze> cadenzeList;

    private List motiviList;

    private Double importoDilazionabileSoloScaduto;

    private Double importoDilazionabileSoloScadutoSenzaRai;

    private Double importoDilazionabileTotale;

    private Double importoDilazionabileTotaleSenzaRai;

    private Double maxNumRateSoloScaduto;

    private List<ModalitaPagamento> modalitaPagamentoList;

}
