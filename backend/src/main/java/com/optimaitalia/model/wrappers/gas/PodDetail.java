package com.optimaitalia.model.wrappers.gas;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.JacksonInvoiceDateDeserializer;
import lombok.Data;

import java.util.Date;

@Data
public class PodDetail {

    private String misuratore;

    private String numeroPdr;

    private String tipologiaPdr;

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    private Date inizioValidita;

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    private Date fineValidita;

    private String inattivabile;

    private String motivazioneScartoAttivazione;

    private Boolean disdettaInviata;

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    private Date dataInvioDisdetta;

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    private Date dataInizioPrelievoPrevista;

    @JsonDeserialize(using = JacksonInvoiceDateDeserializer.class)
    private Date dataEsportazione;

    private String matricolaCorrettore;

    private Boolean correttoreVolumetrico;

    private String matricolaContatore;

    private String descrizioneRemi;

    private String cifreContatore;

    private String cifreCorrettore;

    private String classeContatore;

    private String tipoContatore;

    private String progConsumoAnnuo;

    private String letturaSwitching;

    private String pressioneFornitura;

    private String pressioneFornituraDesc;

    private Integer idcontratto;

    private String consumoflat;

    private String idPuntoGaso;

    private String idTipologiaPDR;

    private String idMotivazione;

    private String descMotivazione;

    private String idUsoEscProm;

    private String descUsoEscProm;

    private String percEsclusione;

    private String percUsoEscProm;

    private String lettura;

    private String statoOperazione;

    private String classePdr;

    private String classeCont;

    private String tipoPdrDescrizione;

    private String descrizioneContatore;

    private String matricolacontatareNChange;

    private String punto;
    
}
