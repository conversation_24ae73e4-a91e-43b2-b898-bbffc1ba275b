package com.optimaitalia.model.wrappers.voice;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;
import lombok.Data;

import java.util.Date;

@Data
public class VariazioniLinea {

    private Integer idContratto;

    private String idCliPrincipale;

    private String dialerCliPrincipale;

    private Sede sede;

    private Linea linea;

    private Servi<PERSON> servizio;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    private Date dataAttivazione;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    private Date dataDisattivazione;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    private Date dataInizioValidita;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    private Date dataFineValidita;

    private Integer progressivoFatturazione;

    private String codiceMigrazioneDon;

    private String codiceMigrazioneOpt;

    private String idCedente;

    private String idVolturante;
}
