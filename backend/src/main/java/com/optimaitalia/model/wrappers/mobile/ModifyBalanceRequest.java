package com.optimaitalia.model.wrappers.mobile;

import com.fasterxml.jackson.annotation.JsonProperty;
import net.sf.oval.constraint.Length;
import net.sf.oval.constraint.Max;
import net.sf.oval.constraint.Min;
import net.sf.oval.constraint.NotNull;

public class ModifyBalanceRequest {

    @NotNull
    @Length(min = 12, max = 12)
    private Long simNumber;

    @NotNull
    @Min(5)
    @Max(50)
    private Integer amount;

    public Long getSimNumber() {
        return simNumber;
    }

    @JsonProperty("simNumber")
    public void setSimNumber(Long simNumber) {
        this.simNumber = simNumber;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }
}
