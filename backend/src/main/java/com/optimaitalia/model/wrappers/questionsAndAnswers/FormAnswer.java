package com.optimaitalia.model.wrappers.questionsAndAnswers;

import com.fasterxml.jackson.annotation.JsonProperty;

public class FormAnswer {

    private String cryptoUriDocument;

    private String uriDocument;

    private String title;

    private String name;

    private String created;

    private FormAnswerMetadata metadata;

    @JsonProperty("cryptoUriDocument")
    public String getCryptoUriDocument() {
        return cryptoUriDocument;
    }

    @JsonProperty("CryptoUriDocument")
    public void setCryptoUriDocument(String cryptoUriDocument) {
        this.cryptoUriDocument = cryptoUriDocument;
    }

    @JsonProperty("uriDocument")
    public String getUriDocument() {
        return uriDocument;
    }

    @JsonProperty("UriDocument")
    public void setUriDocument(String uriDocument) {
        this.uriDocument = uriDocument;
    }

    @JsonProperty("title")
    public String getTitle() {
        return title;
    }

    @JsonProperty("Title")
    public void setTitle(String title) {
        this.title = title;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("Name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("created")
    public String getCreated() {
        return created;
    }
    @JsonProperty("Created")
    public void setCreated(String created) {
        this.created = created;
    }

    @JsonProperty("metadata")
    public FormAnswerMetadata getMetadata() {
        return metadata;
    }

    @JsonProperty("Metadata")
    public void setMetadata(FormAnswerMetadata metadata) {
        this.metadata = metadata;
    }
}
