package com.optimaitalia.model.wrappers.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CheckSubscriptionAutoricaricaResponse {

    @JsonProperty("SubscriptionId")
    private String SubscriptionId;

    @JsonProperty("CodCLiente")
    private String CodCLiente;

    @JsonProperty("DataAttivazione")
    private String DataAttivazione;

    @JsonProperty("Brand")
    private String Brand;

    @JsonProperty("ExpireMonth")
    private Integer ExpireMonth;

    @JsonProperty("ExpireYear")
    private Integer ExpireYear;

    @JsonProperty("CardExpiration")
    private String CardExpiration;

    @JsonProperty("MaskedPan")
    private String MaskedPan;

    @JsonProperty("RicaricaRicorrente")
    private Integer RicaricaRicorrente;

    @JsonProperty("importo")
    private Double importo;

    @JsonProperty("EsitoResponse")
    private EsitoResponse EsitoResponse;

    @JsonProperty("ProviderPagamento")
    private String ProviderPagamento;

    @JsonProperty("EMail")
    private String email;
}
