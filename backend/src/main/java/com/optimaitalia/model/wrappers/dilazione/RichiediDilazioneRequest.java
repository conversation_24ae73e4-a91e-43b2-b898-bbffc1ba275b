package com.optimaitalia.model.wrappers.dilazione;

import lombok.Data;

@Data
public class RichiediDilazioneRequest {

    private RichiediDilazioneFormData formModalData;

    private ResultFormModalData resultFormModalData;

    @Data
    public class RichiediDilazioneFormData {

        private String canoneRai;
        private String casuale;
        private String email;
        private String importoDilazione;
        private String numeroRate;
        private String phoneNumber;
        private Double scadenza;
        private String scadenzaRate;
        private Double scaduto;
        private boolean flagScaduto;
        private String modalitaPagamento;

        public String getCanoneRai() {
            return canoneRai;
        }

        public void setCanoneRai(String canoneRai) {
            this.canoneRai = canoneRai;
        }

        public String getCasuale() {
            return casuale;
        }

        public void setCasuale(String casuale) {
            this.casuale = casuale;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getImportoDilazione() {
            return importoDilazione;
        }

        public void setImportoDilazione(String importoDilazione) {
            this.importoDilazione = importoDilazione;
        }

        public String getNumeroRate() {
            return numeroRate;
        }

        public void setNumeroRate(String numeroRate) {
            this.numeroRate = numeroRate;
        }

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }

        public Double getScadenza() {
            return scadenza;
        }

        public void setScadenza(Double scadenza) {
            this.scadenza = scadenza;
        }

        public String getScadenzaRate() {
            return scadenzaRate;
        }

        public void setScadenzaRate(String scadenzaRate) {
            this.scadenzaRate = scadenzaRate;
        }

        public Double getScaduto() {
            return scaduto;
        }

        public void setScaduto(Double scaduto) {
            this.scaduto = scaduto;
        }

        public boolean isFlagScaduto() {
            return flagScaduto;
        }

        public void setFlagScaduto(boolean flagScaduto) {
            this.flagScaduto = flagScaduto;
        }

        public String getModalitaPagamento() {
            return modalitaPagamento;
        }

        public void setModalitaPagamento(String modalitaPagamento) {
            this.modalitaPagamento = modalitaPagamento;
        }
    }

    @Data
    public class ResultFormModalData {

        private String cadenza;
        private String importo;
        private String importoRata;
        private String numero;
        private String primarata;
        private String modalitaPagamentoDisplay;

        public String getCadenza() {
            return cadenza;
        }

        public void setCadenza(String cadenza) {
            this.cadenza = cadenza;
        }

        public String getImporto() {
            return importo;
        }

        public void setImporto(String importo) {
            this.importo = importo;
        }

        public String getImportoRata() {
            return importoRata;
        }

        public void setImportoRata(String importoRata) {
            this.importoRata = importoRata;
        }

        public String getNumero() {
            return numero;
        }

        public void setNumero(String numero) {
            this.numero = numero;
        }

        public String getPrimarata() {
            return primarata;
        }

        public void setPrimarata(String primarata) {
            this.primarata = primarata;
        }

        public String getModalitaPagamentoDisplay() {
            return modalitaPagamentoDisplay;
        }

        public void setModalitaPagamentoDisplay(String modalitaPagamentoDisplay) {
            this.modalitaPagamentoDisplay = modalitaPagamentoDisplay;
        }

    }
}
