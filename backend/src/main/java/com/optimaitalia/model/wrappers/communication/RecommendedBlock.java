package com.optimaitalia.model.wrappers.communication;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.SelfCarePdfUtils;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;

import java.util.Date;
import java.util.List;

public class RecommendedBlock {

    private Date sendDate;

    private Date recommendedDate;

    private String recommendedUrl;

    private String pdfName;

    private List<Invoice> invoices;

    @JsonProperty("sendDate")
    public Date getSendDate() {
        return sendDate;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("DataInvio")
    public void setSendDate(Date sendDate) {
        this.sendDate = sendDate;
    }

    @JsonProperty("recommendedDate")
    public Date getRecommendedDate() {
        return recommendedDate;
    }

    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    @JsonProperty("DataRaccomandata")
    public void setRecommendedDate(Date recommendedDate) {
        this.recommendedDate = recommendedDate;
    }

    @JsonProperty(value = "recommendedUrl", access = JsonProperty.Access.WRITE_ONLY)
    public String getRecommendedUrl() {
        return recommendedUrl;
    }

    @JsonProperty("UriRaccomandata")
    public void setRecommendedUrl(String recommendedUrl) {
        this.recommendedUrl = recommendedUrl;
        this.pdfName = SelfCarePdfUtils.getFileNameFromUrl(recommendedUrl);
    }

    @JsonProperty("invoices")
    public List<Invoice> getInvoices() {
        return invoices;
    }

    @JsonProperty("Fatture")
    public void setInvoices(List<Invoice> invoices) {
        this.invoices = invoices;
    }

    public String getPdfName() {
        return pdfName;
    }
}
