package com.optimaitalia.model.wrappers.offer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.optimaitalia.model.enums.CausalGroupRowType;

public class CausalGroup {

    private Integer anno;

    private String azione;

    private Integer bundledett;

    private String bundlesCad;

    private Integer canone;

    private Integer cliente;

    private Integer fatt;

    private Long id;

    private Integer idListino;

    private Integer idTipoRiga;

    private Long invoice;

    private Integer invoiceNumber;

    private String invoiceSerie;

    private String mese;

    private String note;

    private String pdr;

    private String pod;

    private String ramo;

    private Boolean rateo;

    private Long rifContoRelax;

    private Integer servizio;

    private Integer stato;

    private Integer tipoItem;

    private CausalGroupRowType tipoRiga;

    private Integer unitaExtra;

    private Integer unitaIncluse;

    private Integer unitaUtilizzate;

    private Integer valore;

    private Double valoreExtra;

    @JsonProperty("anno")
    public Integer getAnno() {
        return anno;
    }

    @JsonProperty("Anno")
    public void setAnno(Integer anno) {
        this.anno = anno;
    }

    @JsonProperty("azione")
    public String getAzione() {
        return azione;
    }

    @JsonProperty("Azione")
    public void setAzione(String azione) {
        this.azione = azione;
    }

    @JsonProperty("bundledett")
    public Integer getBundledett() {
        return bundledett;
    }

    @JsonProperty("Bundledett")
    public void setBundledett(Integer bundledett) {
        this.bundledett = bundledett;
    }

    @JsonProperty("bundlesCad")
    public String getBundlesCad() {
        return bundlesCad;
    }

    @JsonProperty("BundlesCad")
    public void setBundlesCad(String bundlesCad) {
        this.bundlesCad = bundlesCad;
    }

    @JsonProperty("canone")
    public Integer getCanone() {
        return canone;
    }

    @JsonProperty("Canone")
    public void setCanone(Integer canone) {
        this.canone = canone;
    }

    @JsonProperty("cliente")
    public Integer getCliente() {
        return cliente;
    }

    @JsonProperty("Cliente")
    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    @JsonProperty("fatt")
    public Integer getFatt() {
        return fatt;
    }

    @JsonProperty("Fatt")
    public void setFatt(Integer fatt) {
        this.fatt = fatt;
    }

    @JsonProperty("id")
    public Long getId() {
        return id;
    }

    @JsonProperty("Id")
    public void setId(Long id) {
        this.id = id;
    }

    @JsonProperty("idListino")
    public Integer getIdListino() {
        return idListino;
    }

    @JsonProperty("IdListino")
    public void setIdListino(Integer idListino) {
        this.idListino = idListino;
    }

    @JsonProperty("idTipoRiga")
    public Integer getIdTipoRiga() {
        return idTipoRiga;
    }

    @JsonProperty("IdTipoRiga")
    public void setIdTipoRiga(Integer idTipoRiga) {
        this.idTipoRiga = idTipoRiga;
    }

    @JsonProperty("invoice")
    public Long getInvoice() {
        return invoice;
    }

    @JsonProperty("Invoice")
    public void setInvoice(Long invoice) {
        this.invoice = invoice;
    }

    @JsonProperty("invoiceNumber")
    public Integer getInvoiceNumber() {
        return invoiceNumber;
    }

    @JsonProperty("InvoiceNumber")
    public void setInvoiceNumber(Integer invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    @JsonProperty("invoiceSerie")
    public String getInvoiceSerie() {
        return invoiceSerie;
    }

    @JsonProperty("InvoiceSerie")
    public void setInvoiceSerie(String invoiceSerie) {
        this.invoiceSerie = invoiceSerie;
    }
    @JsonProperty("mese")
    public String getMese() {
        return mese;
    }
    @JsonProperty("Mese")
    public void setMese(String mese) {
        this.mese = mese;
    }
    @JsonProperty("note")
    public String getNote() {
        return note;
    }
    @JsonProperty("Note")
    public void setNote(String note) {
        this.note = note;
    }
    @JsonProperty("pdr")
    public String getPdr() {
        return pdr;
    }
    @JsonProperty("Pdr")
    public void setPdr(String pdr) {
        this.pdr = pdr;
    }
    @JsonProperty("pod")
    public String getPod() {
        return pod;
    }
    @JsonProperty("Pod")
    public void setPod(String pod) {
        this.pod = pod;
    }
    @JsonProperty("ramo")
    public String getRamo() {
        return ramo;
    }
    @JsonProperty("Ramo")
    public void setRamo(String ramo) {
        this.ramo = ramo;
    }
    @JsonProperty("rateo")
    public Boolean getRateo() {
        return rateo;
    }
    @JsonProperty("Rateo")
    public void setRateo(Boolean rateo) {
        this.rateo = rateo;
    }
    @JsonProperty("rifContoRelax")
    public Long getRifContoRelax() {
        return rifContoRelax;
    }
    @JsonProperty("RifContoRelax")
    public void setRifContoRelax(Long rifContoRelax) {
        this.rifContoRelax = rifContoRelax;
    }
    @JsonProperty("servizio")
    public Integer getServizio() {
        return servizio;
    }
    @JsonProperty("Servizio")
    public void setServizio(Integer servizio) {
        this.servizio = servizio;
    }
    @JsonProperty("stato")
    public Integer getStato() {
        return stato;
    }
    @JsonProperty("Stato")
    public void setStato(Integer stato) {
        this.stato = stato;
    }
    @JsonProperty("tipoItem")
    public Integer getTipoItem() {
        return tipoItem;
    }
    @JsonProperty("TipoItem")
    public void setTipoItem(Integer tipoItem) {
        this.tipoItem = tipoItem;
    }
    @JsonProperty("tipoRiga")
    public CausalGroupRowType getTipoRiga() {
        return tipoRiga;
    }
    @JsonProperty("TipoRiga")
    public void setTipoRiga(CausalGroupRowType tipoRiga) {
        this.tipoRiga = tipoRiga;
    }
    @JsonProperty("unitaExtra")
    public Integer getUnitaExtra() {
        return unitaExtra;
    }
    @JsonProperty("UnitaExtra")
    public void setUnitaExtra(Integer unitaExtra) {
        this.unitaExtra = unitaExtra;
    }
    @JsonProperty("unitaIncluse")
    public Integer getUnitaIncluse() {
        return unitaIncluse;
    }
    @JsonProperty("UnitaIncluse")
    public void setUnitaIncluse(Integer unitaIncluse) {
        this.unitaIncluse = unitaIncluse;
    }
    @JsonProperty("unitaUtilizzate")
    public Integer getUnitaUtilizzate() {
        return unitaUtilizzate;
    }
    @JsonProperty("UnitaUtilizzate")
    public void setUnitaUtilizzate(Integer unitaUtilizzate) {
        this.unitaUtilizzate = unitaUtilizzate;
    }
    @JsonProperty("valore")
    public Integer getValore() {
        return valore;
    }
    @JsonProperty("Valore")
    public void setValore(Integer valore) {
        this.valore = valore;
    }
    @JsonProperty("valoreExtra")
    public Double getValoreExtra() {
        return valoreExtra;
    }
    @JsonProperty("ValoreExtra")
    public void setValoreExtra(Double valoreExtra) {
        this.valoreExtra = valoreExtra;
    }
}
