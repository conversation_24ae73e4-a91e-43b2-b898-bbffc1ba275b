package com.optimaitalia.model.wrappers.mobile.products;

public class ProductIncompatibility {

    private Integer id;

    private Integer incompatibleId;

    private Integer productId;

    private String reason;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIncompatibleId() {
        return incompatibleId;
    }

    public void setIncompatibleId(Integer incompatibleId) {
        this.incompatibleId = incompatibleId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
