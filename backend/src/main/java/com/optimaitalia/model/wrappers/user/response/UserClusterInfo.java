package com.optimaitalia.model.wrappers.user.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.utils.dateDeserializer.CommunicationDateDeserializer;

import java.util.Date;

public class UserClusterInfo {
    private Integer clientId;
    private Double lftValue;
    private Double lifetime;
    private Date data;
    private String caringCluster;
    private String tipoCliente;
    private Boolean display;

    @JsonProperty("clientId")
    public Integer getClientId() {
        return clientId;
    }

    @JsonProperty("ClientId")
    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    @JsonProperty("lftValue")
    public Double getLftValue() {
        return lftValue;
    }

    @JsonProperty("LftValue")
    public void setLftValue(Double lftValue) {
        this.lftValue = lftValue;
    }

    @JsonProperty("lifetime")
    public Double getLifetime() {
        return lifetime;
    }

    @JsonProperty("Lifetime")
    public void setLifetime(Double lifetime) {
        this.lifetime = lifetime;
    }

    @JsonProperty("data")
    @JsonDeserialize(using = CommunicationDateDeserializer.class)
    public Date getData() {
        return data;
    }

    @JsonProperty("Data")
    public void setData(Date data) {
        this.data = data;
    }

    @JsonProperty("caringCluster")
    public String getCaringCluster() {
        return caringCluster;
    }

    @JsonProperty("CaringCluster")
    public void setCaringCluster(String caringCluster) {
        this.caringCluster = caringCluster;
    }

    @JsonProperty("tipoCliente")
    public String getTipoCliente() {
        return tipoCliente;
    }

    @JsonProperty("TipoCliente")
    public void setTipoCliente(String tipoCliente) {
        this.tipoCliente = tipoCliente;
    }

    @JsonProperty("display")
    public Boolean getDisplay() {
        return display;
    }

    @JsonProperty("Display")
    public void setDisplay(Boolean display) {
        this.display = display;
    }
}
