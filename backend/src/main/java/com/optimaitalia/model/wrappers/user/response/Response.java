package com.optimaitalia.model.wrappers.user.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class Response {

    private Integer changeId;

    private String ticketnbr;

    private Change ChangeObject;

    @JsonIgnore
    private OriginalRequest originalRequest;

    private class Change {

        private Integer changeId;

        private Integer stateId;

        private String stateDesc;

        private String reason;

        private String reasonPath;

    }
}
