package com.optimaitalia.model.wrappers.mobile.products;

import java.util.List;

public class ProductMapping {

    private Integer costoAttivazione;

    private Float costoRinnovo;

    private String descrizioneProdotto;

    private Boolean flagSoloDati;

    private Long idCluster;

    private Long idProdotto;

    private Long idProdottoEnabler;

    private Long idProfiloTariffario;

    private Long idTipoPeriodoRinnovo;

    private Long idTipoProdotto;

    private String nomeCluster;

    private String nomeProdotto;

    private String nomeProfiloTariffario;

    private String nomeTipoPeriodoRinnovo;

    private Boolean optionReqFlag;

    private Integer periodoRinnovo;

    private List<Dettaglio> dettaglio;

//    private Object nomeTipoProdotto;

//    private Object costoAnnuo;


    public Integer getCostoAttivazione() {
        return costoAttivazione;
    }

    public void setCostoAttivazione(Integer costoAttivazione) {
        this.costoAttivazione = costoAttivazione;
    }

    public Float getCostoRinnovo() {
        return costoRinnovo;
    }

    public void setCostoRinnovo(Float costoRinnovo) {
        this.costoRinnovo = costoRinnovo;
    }

    public String getDescrizioneProdotto() {
        return descrizioneProdotto;
    }

    public void setDescrizioneProdotto(String descrizioneProdotto) {
        this.descrizioneProdotto = descrizioneProdotto;
    }

    public Boolean getFlagSoloDati() {
        return flagSoloDati;
    }

    public void setFlagSoloDati(Boolean flagSoloDati) {
        this.flagSoloDati = flagSoloDati;
    }

    public Long getIdCluster() {
        return idCluster;
    }

    public void setIdCluster(Long idCluster) {
        this.idCluster = idCluster;
    }

    public Long getIdProdotto() {
        return idProdotto;
    }

    public void setIdProdotto(Long idProdotto) {
        this.idProdotto = idProdotto;
    }

    public Long getIdProdottoEnabler() {
        return idProdottoEnabler;
    }

    public void setIdProdottoEnabler(Long idProdottoEnabler) {
        this.idProdottoEnabler = idProdottoEnabler;
    }

    public Long getIdProfiloTariffario() {
        return idProfiloTariffario;
    }

    public void setIdProfiloTariffario(Long idProfiloTariffario) {
        this.idProfiloTariffario = idProfiloTariffario;
    }

    public Long getIdTipoPeriodoRinnovo() {
        return idTipoPeriodoRinnovo;
    }

    public void setIdTipoPeriodoRinnovo(Long idTipoPeriodoRinnovo) {
        this.idTipoPeriodoRinnovo = idTipoPeriodoRinnovo;
    }

    public Long getIdTipoProdotto() {
        return idTipoProdotto;
    }

    public void setIdTipoProdotto(Long idTipoProdotto) {
        this.idTipoProdotto = idTipoProdotto;
    }

    public String getNomeCluster() {
        return nomeCluster;
    }

    public void setNomeCluster(String nomeCluster) {
        this.nomeCluster = nomeCluster;
    }

    public String getNomeProdotto() {
        return nomeProdotto;
    }

    public void setNomeProdotto(String nomeProdotto) {
        this.nomeProdotto = nomeProdotto;
    }

    public String getNomeProfiloTariffario() {
        return nomeProfiloTariffario;
    }

    public void setNomeProfiloTariffario(String nomeProfiloTariffario) {
        this.nomeProfiloTariffario = nomeProfiloTariffario;
    }

    public String getNomeTipoPeriodoRinnovo() {
        return nomeTipoPeriodoRinnovo;
    }

    public void setNomeTipoPeriodoRinnovo(String nomeTipoPeriodoRinnovo) {
        this.nomeTipoPeriodoRinnovo = nomeTipoPeriodoRinnovo;
    }

    public Boolean getOptionReqFlag() {
        return optionReqFlag;
    }

    public void setOptionReqFlag(Boolean optionReqFlag) {
        this.optionReqFlag = optionReqFlag;
    }

    public Integer getPeriodoRinnovo() {
        return periodoRinnovo;
    }

    public void setPeriodoRinnovo(Integer periodoRinnovo) {
        this.periodoRinnovo = periodoRinnovo;
    }

    public List<Dettaglio> getDettaglio() {
        return dettaglio;
    }

    public void setDettaglio(List<Dettaglio> dettaglio) {
        this.dettaglio = dettaglio;
    }
}

