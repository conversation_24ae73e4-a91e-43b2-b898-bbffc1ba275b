package com.optimaitalia.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum IncidentEvent {

    GUASTO_ADSL("GUASTO_ADSL"),
    AUTOLETTURA("Autolettura"),
    PERSONAL_DATA_CHANGE("Variazione Anagrafica"),
    INFORMAZIONE_COMMERCIALE("Informazione Commerciale"),
    RECONTACT_REQUEST("Richiesta Ricontatto"),
    VARIAZIONE_COMMERCIALE("Variazione Commerciale"),
    VARIAZIONE_FATTURAZIONE("Variazione Fatturazione"),
    RIMODULAZIONE_OFFERTA_SELFCARE("Rimodulazione Offerta SelfCare"),
    DISATTIVAZIONE_AMAZON_PRIME("Disattivazione Amazon Prime"),
    VARIAZIONE_PROMO3MESI("Variazione Promo3Mesi"),
    PAGAMENTO_FATTURA_FLESSIBLE("Pagamento Fattura Flessibile"),
    MNP_IN_POST_ATTIVAZIONE("MNP IN Post Attivazione"),
    VISIT_AGENT("Informazione Richiesta visita Agente"),
    CONTRACT_REQUEST("Informazione Invio contratto"),
    ALLEGO_PAGAMENTO("Variazione Incassi da fax"),
    CROSS_SELLING("Informazione CrossSelling"),
    REDEEM_ENERGY_VOUCHER("Sconto Energy Card"),
    REDEEM_MOBILE_VOUCHER("Sconto Mobile Card"),
    OFFER_5G("Attivazione5G"),
    DEACTIVATION_5G("Disattivazione5G");

    private final String incidentEvent;

    IncidentEvent(String incidentEvent) {
        this.incidentEvent = incidentEvent;
    }

    @JsonValue
    public String getValue() {
        return this.incidentEvent;
    }


    @Override
    public String toString() {
        return "IncidentEvent{" +
                "incidentEvent='" + incidentEvent + '\'' +
                '}';
    }
}
