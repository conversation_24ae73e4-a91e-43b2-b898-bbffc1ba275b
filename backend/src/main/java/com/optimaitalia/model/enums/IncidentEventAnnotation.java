package com.optimaitalia.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum IncidentEventAnnotation {
    REQUEST_FOR_PERSONAL_CHANGES("Richiesta variazione anagrafica");

    private final String incidentEventAnnotation;


    IncidentEventAnnotation(String incidentEventAnnotation) {
        this.incidentEventAnnotation = incidentEventAnnotation;
    }

    @JsonValue
    public String getValue() {
        return this.incidentEventAnnotation;
    }
}
