package com.optimaitalia.repository;

import com.optimaitalia.model.db.NotificaMdp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface NotificaMdpRepository extends JpaRepository<NotificaMdp, Long> {
    @Query(value = "SELECT mdp FROM NotificaMdp mdp where mdp.clientId = ?1")
    List<NotificaMdp> findAllById(Long id);
}
