optima:
  api-host: https://api.optimaitalia.com
restdata:
  token: basic Wm5EZWlmbFhMSmpidUhEYTVCSzA5eWZ1SzJRYTpheUJNNG5JVHc5dlFVY0RUc1U0ZFhmUDVlQ0lh
  urls:
    userauthenticate: https://api.optimaitalia.com:8493/public/customer/v1.0.0/authenticateUser
    userauthenticateFake: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.AuthenticateUser-API/fake
    smsService: http://wso2-esb.optimaitalia.com:8300/api/governance.communications.Sms-API
    token: https://api.optimaitalia.com:8493/token
    chart: https://api.optimaitalia.com:8493/public/customer/getInfoServiziCliente/{clientId}
    userdata: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.Detail-API/{clientId}
    business-agent: http://wso2-esb.optimaitalia.com:8300/api/governance.sale.GetAgentiAssegnatiTargetCliente-API
    #userdata: https://api.optimaitalia.com:8493/public/customer/{clientId}/detail
    #userdata: http://mi-areaclienti.optimaitalia.com:13000/profilo?idCliente={clientId}
    contracts: http://wso2-esb.optimaitalia.com:8300/api/governance.contracts.GetContrattiByParams-API/withCheck
    #invoices: https://api.optimaitalia.com:8493/public/customer/v1.0.0/getInvoiceByClienteFatt/withPaymentStatus
    invoices: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetInvoiceByClienteFatt-API/withPaymentStatus
    billing-center: http://wso2-esb.optimaitalia.com:8300/api/governance.provisioning.integration.ExecuteQuery-API/SYS_PROV_PA_Selfcare
    services: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.UltimoStatoUtenze-API/{clientId}/0
    #services: http://mi-areaclienti.optimaitalia.com:13000/ultimoStatoUtenza?idCliente={clientId}
    incident-event: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.IncidentEvent-API
    autolettura-ee: http://wso2-esb.optimaitalia.com:8300/api/governance.electricity.InsertAutoletturaEE-API
    autolettura-gas: http://wso2-esb.optimaitalia.com:8300/api/governance.gas.InsertAutoletturaGAS-API
    #spedizionetestata: https://api.optimaitalia.com:8493/public/shippings/v1.0.0/search
    spedizionetestata: http://mi-areaclienti.optimaitalia.com:13000/spedizioni
    #spedizione: https://api.optimaitalia.com:8493/public/shippings/v1.0.0/detail
    spedizione: http://mi-areaclienti.optimaitalia.com:13000/spedizioniDettaglio
    formAnswers: http://wso2-esb:8300/api/governance.support.document.RicercaModulo-API
    account: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetSituazioneContabileCliente-API/{clientId}
    сlientiBundle: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.GetClientiBundleAll-API
    sciolto: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.Bundle-API?gruppoVendita=CVM&idcluster={cluster}&crossSelling=S&codiceBundle={offer}
    generalSciolito: http://wso2-esb.optimaitalia.com:8300/api/governance.campaign.IsInLastTarget-API/1265?customerId={clientId}
    #warning link
    mnpPostActivation: http://wso2-esb.optimaitalia.com:8300/api/governance.incident.MnpPostActivation-API?createIncident=true&origine=selfcare
    mnpUploadFiles: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.UploadFile-API
    payPalActivation: http://wso2-esb.optimaitalia.com:8300//api/governance.payment.Paypal-API/creaOrdine
    offers-data: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.GetDatiOfferta-API/smart
    #offers-data: http://mi-areaclienti.optimaitalia.com:13000/contoRelax
    #modalitaPagamento: https://api.optimaitalia.com:8493/public/customer/getIbanSDD/{clientId}
    сredit-policy-status: http://wso2-esb.optimaitalia.com:8300/api/governance.Credito.getUltimoStatoCreditPolicy-API/{clientId}
    modalitaPagamento: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetIbanSDD-API/{clientId}
    grantModalitaPagamento: http://wso2-esb.optimaitalia.com:8300/api/governance.integration.Execute-API
    getPDF: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.GetFile-API?relUrl=
    getFatturaNc: http://wso2-esb.optimaitalia.com:8300/api/governance.support.document.GetFatturaNc-API?tipo=COMPLETA&relUrl=
    segnalazione: http://wso2-esb.optimaitalia.com:8300/api/governance.incident.Incident-API/ByCustomer/{clientId}
    electricity-pod-details: https://api.optimaitalia.com:8493/public/electricity/getPodDetails
    electricity-autolettura-2g: http://wso2-esb.optimaitalia.com:8300/api/governance.eos.GetInfoPod-API
    electricity-point-adjustments: https://api.optimaitalia.com:8493/public/electricity/v1.0.0/getConguaglioDetail
    electricity-details-by-hours: http://wso2-esb.optimaitalia.com:8300/api/governance.optimapower.GetConsumiDettagliOre-API
    voice-client-cli: https://api.optimaitalia.com:8493/public/voice/v1.0.0/getVoceCliCliente/{clientId}
    adsl-client-cli: https://api.optimaitalia.com:8493/public/broadband/v1.0.0/getLineeClienteADSL/{clientId}
    gas-pod-details: https://api.optimaitalia.com:8493/public/gas/v1.0.0/getInfoPdrByPdr
    gas-pdr-additional-data: https://api.optimaitalia.com:8493/public/gas/v1.0.0/getAltriDatiPdrByPdr
    gas-point-adjustments: http://wso2-esb.optimaitalia.com:8300/api/governance.gas.GetConguagliPuntiGas-API
    contract-transparency-pdf-data: https://api.optimaitalia.com:8493/public/customer/v1.0.0/getOfferteTlc
    communication-email-info: https://api.optimaitalia.com:8493/public/customer/v1.0.0/email/filtered/getByAccountId/
    recommendations-blocks: https://api.optimaitalia.com:8493/public/customer/v1.0.0/getRaccomandateBlocco/{clientId}
    router: https://api.optimaitalia.com:8493/public/broadband/v1.0.0/getLineeClienteADSL/{clientId}
    schedaTecnicaLinea: https://api.optimaitalia.com:8493/public/broadband/v1.0.0/getSchedaTecnicaLinea/{IdLinea}
    initial-bonus-progress: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.GetAndamentoContoRelaxEBonusIniziale-API
    check-tariff: http://www.optimaitalia.com/toolallegatoillustrativo/ajax.php
    getSchedaTecnicaVoce: https://api.optimaitalia.com:8493/public/voice/v1.0.0/getSchedaTecnicaVoce
    сomunicazioniNote: https://api.optimaitalia.com:8493/public/customer/v1.0.0/getNote/{clientId}
    condominio: http://wso2-esb.optimaitalia.com:8300/api/governance.services.crm.Dynamic-API/crm/customer/{clientId}/condomini
    сondominioFiscalCode: http://wso2-esb.optimaitalia.com:8300/api/governance.dwh.Integration.ExecuteQuery-API/SYS_DWH_GetAmministratoreCondominio
    salesMail: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetSalesByCliente-API/{clientId}
    dilazioneCliente: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.GetDilazioneCliente-API/{clientId}
    richiediDilazione: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.VerificaDilazioneExt-API
    pagamentoFlessibile: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.VerificaFatturaFlessibile-API/{customer_id}
    saveRichiediDilazione: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.RichiestaDilazioneExt-API
    recontact-request-url: http://wso2-esb.optimaitalia.com:8300/api/governance.avaya.AppendContactsToCampaign-API/async
    customer-cluster-info-url: http://wso2-esb.optimaitalia.com:8300/api/governance.integration.GetClusterByUser-API
    amazon-prime-data: http://wso2-esb.optimaitalia.com:8300/api/governance.docomo.IsEligible-API/v2/{clientId}
    amazon-prime-unsubscribe-url: http://wso2-esb.optimaitalia.com:8300/api/governance.docomo.Unsubscribe-API/{clientId}
    #amazon-prime-unsubscribe-url: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.Amazon-API/unsubscribe/{clientId}
    reportingAttachment: http://wso2-esb.optimaitalia.com:8300/api/governance.incident.CreaNota-API
    meseOff: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.PianificazionePromo-API
    change-promo-mese: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.ChangePromoMeseOff-API
    movimentazioni: http://wso2-esb.optimaitalia.com:8300/api/governance.offer.GetMovimentazioniContoRelax-API
    auditLog: http://wso2-esb.optimaitalia.com:8300/api/governance.support.monitoring.SystemMonitoring-API/auditLog
    voucher-card-status: http://wso2-esb.optimaitalia.com:8300/api/governance.fringebenefit.GetDetailsByCodice-API?codice=
    voucher-card-redeem: http://wso2-esb.optimaitalia.com:8300/api/governance.fringebenefit.AssociaVaucherCliente-API
    voucher-card-user-information: http://wso2-esb.optimaitalia.com:8300/api/governance.fringebenefit.GetAssociazioni-API?idCliente={clientId}&idTipoCard={typeCard}
    voucher-card-contracts: http://wso2-esb.optimaitalia.com:8300/api/governance.provisioning.integration.ExecuteQuery-API/USR_PROV_vContrattiByParams
    voucher-card-sim-balance: http://wso2-esb.optimaitalia.com:8300/api/governance.dwh.Integration.ExecuteQuery-API/SYS_SimCreditoResiduo
    voucher-card-top-up-sim: http://wso2-esb.optimaitalia.com:8300/api/governance.services.provisioning.Dynamic-API/mobile/createGenericOperation.json
    porta-un-amico: http://wso2-esb.optimaitalia.com:8300/api/governance.crm.Coupon-API/{clientId}
    app-offer-5G: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.AddOn-API/vendibilita
    condominio-debt: http://wso2-esb-pp.optimaitalia.com:8300/api/governance.customer.Condomini-API/byCustomer/{clientId}
    #prospect users
    contracts-by-cf: http://wso2-esb.optimaitalia.com:8300/api/governance.provisioning.integration.ExecuteQuery-API/SYS_PROV_vContrattiPerCF
    prospect-email-support: http://wso2-esb.optimaitalia.com:8300/api/governance.communications.EmailStream-API
    prospect-file-upload: http://wso2-esb.optimaitalia.com:8300/api/governance.contracts.Upload-API/{fileName}/{contractId}/test/test/1/1
mobile:
  service:
    url:
      #sim-per-client: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/contratti
      user-geolocation: http://api.ipstack.com/check?access_key=********************************
      sim-per-client: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.Subscription-API
      #      residuo: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/wallet
      sim-details: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.SubscriptionDetails-API
      sim-balance: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.Wallet-API
      #sim-details: http://mi-areaclienti-01.optimaitalia.com:3000/mobile
      product: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/product/selfcare
      product-offer: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.IVR-API/prodotto
      product-activations: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/productActivations
      product-activations-external: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/external/productActivations
      traffic-details: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/callLogs
      change-tariff: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/cambioPiano
      change-options: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/cambioOpzioni
      modify-balance: https://api.optimaitalia.com:8493/public/mobile/v1.0.0/ricarica
      ricarica-sisal: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.IVR-API/ricarica
      #contrattiMobile: http://mi-areaclienti.optimaitalia.com:13000/contrattiMobile?idCliente={clientId}
      contrattiMobile: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.Contratti-API?customerId={clientId}
      prodPurchPromo: http://wso2-esb.optimaitalia.com:8300/api/external.mobile.ProdPurchPromo-API?productPurchaseId={productId}
      mobile-operators: http://wso2-esb.optimaitalia.com:8300/api/governance.mobile.MobileOperators-API?visible=true
  properties:
    ricarica-sim:
      url:
        success: http://mi-areaclienti-pre.optimaitalia.com/faidate/servizi-attivi
        default: http://mi-areaclienti-pre.optimaitalia.com/faidate/servizi-attivi
user:
  change:
    key: 2314640E-072C-478F-A949-88F748026C43
    change-personal-data-url: http://wso2-esb.optimaitalia.com:8300/api/governance.customer.VariazioneAnagrafica-API
    change-password-identification-url: http://wso2-esb.optimaitalia.com:8300/api/governance.crm.IdPwdChange-API
    change-shipping-type-url: ${optima.api-host}:8493/public/customer/v1.0.0/shippingType
    recipient-code-change-url: ${optima.api-host}:8493/public/customer/customDestCodeChange
    origin: 200007
security:
  sso-token: https://api-pp1.optimaitalia.com:8493/public/secured/myoptima/validateUser/{clientId}
mese:
  codicePromo: PR_54
