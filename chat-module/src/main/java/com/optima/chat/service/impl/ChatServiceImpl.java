package com.optima.chat.service.impl;

import com.optima.chat.exceptions.ChatInitializationException;
import com.optima.chat.service.ChatService;
import com.optima.chat.service.CustomerService;
import com.optima.chat.service.SkillSetService;
import com.optima.chat.wsdl.customer.RequestTextChat;
import com.optima.chat.wsdl.customer.RequestTextChatResponse;
import com.optima.chat.wsdl.skill.IsSkillsetInService;
import com.optima.chat.wsdl.skill.IsSkillsetInServiceResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
public class ChatServiceImpl implements ChatService {

    private static final Logger logger = LogManager.getLogger(ChatServiceImpl.class);

    private final CustomerService customerService;

    private final SkillSetService skillSetService;

    public ChatServiceImpl(CustomerService customerService, SkillSetService skillSetService) {
        this.customerService = customerService;
        this.skillSetService = skillSetService;
    }

    @Override
    public RequestTextChatResponse requestTextChat(RequestTextChat requestTextChat) {
        logger.info("Preparing text chat initialization.");
        if (requestTextChat != null) {
            IsSkillsetInService isSkillsetInService = new IsSkillsetInService();
            isSkillsetInService.setSessionKey(requestTextChat.getSessionKey());
            if (requestTextChat.getNewContact() != null) {
                isSkillsetInService.setSkillsetID(requestTextChat.getNewContact().getSkillsetID());
            }
            logger.info("Initializing text chat.");
            IsSkillsetInServiceResponse skillsetInServiceResponse = skillSetService.isSkillsetInServiceResponse(isSkillsetInService);
            if (skillsetInServiceResponse != null && skillsetInServiceResponse.isIsSkillsetInServiceResult()) {
                logger.info("Text chat has been initialized.");
                return customerService.requestTextChat(requestTextChat);
            }
            throw new ChatInitializationException("Error while trying to initialize text chat. Response is null or skill set is not served.");
        }
        throw new ChatInitializationException("It's not possible to setup text chat. Response is null.");
    }
}
