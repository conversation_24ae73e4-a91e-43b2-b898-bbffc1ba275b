package com.optima.security.model.authenticateUserObject;

import com.optima.security.model.userData.Addresses;
import com.optima.security.model.userData.UserData;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class AuthenticateUserObject {

    private static final Logger logger = LogManager.getLogger(AuthenticateUserObject.class);
    private Boolean authenticated;
    private String passwordModifiedDate;
    private CustomerData customerData = null;
    private String description = null;
    private Boolean success = false;

    public AuthenticateUserObject(UserData userData, String passwordModifiedDate) {
        if (userData != null && userData.getId() != 0) {
            this.authenticated = true;
            this.passwordModifiedDate = passwordModifiedDate;
            this.customerData = new CustomerData();
            this.customerData.setIdCliente(userData.getId());
            this.customerData.setCrmGuiId(userData.getAccountId());
            this.customerData.setConsumer(userData.getCluster().getValue().toLowerCase().equals("consumer"));
            this.customerData.setSottotipoCluster(userData.getSottotipoCluster());

            this.customerData.setNome(!userData.getFirstName().equals("") ? userData.getFirstName() : userData.getName());
            this.customerData.setCognome(userData.getLastName());
            this.customerData.setPartitaIva(userData.getVatNumber());
            this.customerData.setCodiceFiscale(userData.getFiscalCode());
            this.customerData.setTipoFatturazione(userData.getBillingType().toLowerCase());

            IndirizzoEmail indirizzoEmail = new IndirizzoEmail();
            indirizzoEmail.setKey(userData.getEmail());
            indirizzoEmail.setValue(userData.getEmail());
            this.customerData.setIndirizzoEmail(indirizzoEmail);

            SedeLegale sedeLegale = new SedeLegale();
            Addresses addresses = userData.getAddresses().stream().filter(item -> item.getTipo().equalsIgnoreCase("sede legale"))
                    .collect(Collectors.collectingAndThen(Collectors.toList(), list -> !list.isEmpty() ? list.get(0) : null));
            if (addresses != null) {
                try {
                    sedeLegale.setDescrizione(addresses.getIndirizzo()
                            .substring(0, addresses.getIndirizzo().indexOf(",", addresses.getIndirizzo().indexOf(",") + 1)));
                    sedeLegale.setCap(addresses.getIndirizzo()
                            .substring(addresses.getIndirizzo().lastIndexOf(", ") + 2, addresses.getIndirizzo().lastIndexOf(" ")));
                } catch (Exception e) {
                    sedeLegale.setDescrizione(addresses.getIndirizzo());
                }
            }
            this.customerData.setSedeLegale(sedeLegale);

            IndirizzoFatturazione indirizzoFatturazione = new IndirizzoFatturazione();

            List<Addresses> listIndrizzo = userData.getAddresses().stream().filter(item -> item.getTipo().toLowerCase().equals("fatturazione"))
                    .collect(Collectors.toList());
            addresses = listIndrizzo.size() > 0 ? listIndrizzo.get(listIndrizzo.size() - 1) : null;

            if (addresses != null) {
                try {
                    indirizzoFatturazione.setDescrizione(addresses.getIndirizzo()
                            .substring(0, addresses.getIndirizzo().indexOf(",", addresses.getIndirizzo().indexOf(",") + 1)));
                    indirizzoFatturazione.setCap(addresses.getIndirizzo()
                            .substring(addresses.getIndirizzo().lastIndexOf(", ") + 2, addresses.getIndirizzo().lastIndexOf(" ")));
                } catch (Exception e) {
                    indirizzoFatturazione.setDescrizione(addresses.getIndirizzo());
                }

            }
            this.customerData.setIndirizzoFatturazione(indirizzoFatturazione);
            List<NumeriRiferimento> numeriRiferimentoList = new ArrayList<>();
            userData.getReferenceNumbers().forEach(item -> {
                NumeriRiferimento numeriRiferimento = new NumeriRiferimento();
                String temp = item.getRecapito().replace("+", "");
                numeriRiferimento.setKey(temp);
                numeriRiferimento.setValue(temp);
                numeriRiferimentoList.add(numeriRiferimento);
            });
            if (!numeriRiferimentoList.isEmpty()) {
                this.customerData.setNumeriRiferimento(numeriRiferimentoList);
            }

        } else {
            logger.error("There is no user data from restdata services. The user cannot be authenticated.");
            this.authenticated = false;
        }
    }

    public Boolean getAuthenticated() {
        return authenticated;
    }

    public void setAuthenticated(Boolean authenticated) {
        this.authenticated = authenticated;
    }

    public CustomerData getCustomerData() {
        return customerData;
    }

    public void setCustomerData(CustomerData customerData) {
        this.customerData = customerData;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getPasswordModifiedDate() {
        return passwordModifiedDate;
    }

    public void setPasswordModifiedDate(String passwordModifiedDate) {
        this.passwordModifiedDate = passwordModifiedDate;
    }
}
