package com.optima.security.factory.impl;

import com.optima.security.constants.TokenType;
import com.optima.security.factory.TokenServiceFactory;
import com.optima.security.processors.TokenService;

import java.util.HashMap;
import java.util.Map;

import java.util.List;

public class TokenServiceFactoryImpl implements TokenServiceFactory {

    private Map<TokenType, TokenService> processorMap;

    public TokenServiceFactoryImpl(List<TokenService> list) {
        this.processorMap = new HashMap<>();
        if (list != null) {
            for (TokenService processor : list) {
                if (processor.getProcessorType() != null) {
                    processorMap.put(processor.getProcessorType(), processor);
                }
            }
        }

    }


    @Override
    public TokenService getService(TokenType tokenType) {
        return processorMap.get(tokenType);
    }
}
