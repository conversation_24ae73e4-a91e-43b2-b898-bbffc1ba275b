package com.optima.security;

import com.optima.security.model.SecurityConfig;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@ComponentScan("com.optima.security")
@EnableConfigurationProperties(SecurityConfig.class)
@EnableTransactionManagement
@EnableJpaRepositories("com.optima.security.repository")
@EntityScan("com.optima.security.model")
public class SecurityConfiguration {
}
