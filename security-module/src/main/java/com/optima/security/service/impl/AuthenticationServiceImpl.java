package com.optima.security.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optima.security.model.User;
import com.optima.security.model.UserEntity;
import com.optima.security.model.UserLoggedInBefore;
import com.optima.security.model.userData.PaymentData;
import com.optima.security.model.userData.UserData;
import com.optima.security.repository.UserLoggedInBeforeRepository;
import com.optima.security.repository.dao.UserDao;
import com.optima.security.service.AuthenticationService;
import com.optima.security.service.SecurityService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

@Service
public class AuthenticationServiceImpl implements AuthenticationService {

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper;

    private final SecurityService securityService;

    private final UserDao userDao;

    private final UserLoggedInBeforeRepository userLoggedInBeforeRepository;

    private final SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

    private static final Logger logger = LogManager.getLogger(AuthenticationServiceImpl.class);

    private @Value("${restdata.token}")
    String token;
    private @Value("${restdata.urls.token}")
    String tokenUrl;
    private @Value("${restdata.urls.userauthenticate}")
    String loginUrl;
    private @Value("${restdata.urls.userauthenticateFake}")
    String loginFakeUrl;
    private @Value("${security.sso-token}")
    String ssoToken;

    @Autowired
    public AuthenticationServiceImpl(UserDao userDao, RestTemplate restTemplate, ObjectMapper objectMapper,
                                     SecurityService securityService, UserLoggedInBeforeRepository userLoggedInBeforeRepository) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.securityService = securityService;
        this.userLoggedInBeforeRepository = userLoggedInBeforeRepository;
        this.userDao = userDao;
    }

    @Override
    public User login(String username, String password) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        //headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("username", username);
        body.put("password", password);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        Map response = restTemplate.postForObject(loginUrl, request, Map.class);
        return objectMapper.convertValue(response.get("customerData"), User.class);
    }



    @Override
    public User loginFake(String username, String password) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("username", username);
        body.put("password", password);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        Map response = restTemplate.postForObject(loginFakeUrl, request, Map.class);
        return objectMapper.convertValue(response.get("customerData"), User.class);
    }

    @Override
    public UserData getUserData(String clientId, String url) {
        Map<String, Object> dataForUser = getDataForUser(clientId, url);
        if (dataForUser != null && dataForUser.get("response") != null) {
            return objectMapper.convertValue(dataForUser.get("response"), UserData.class);
        }
        return new UserData();
    }

    @Override
    public PaymentData getUserPaymentData(String clientId, String url) {
        Map<String, Object> dataForUser = getDataForUser(clientId, url);
        if (dataForUser != null && dataForUser.get("datiPagamentoCliente") != null) {
            return objectMapper.convertValue(dataForUser.get("datiPagamentoCliente"), PaymentData.class);
        }
        return new PaymentData();
    }


    @Override
    public ResponseEntity<Boolean> checkIfUserAlreadyRestoredPassword(String username, String password) {
        UserEntity user = userDao.findByUserName(username);
        if (user == null) {
            user = userDao.findByEmail(username);
        }
        if (user != null) {
            try {
                String passwordModifiedDate = "14/09/2020";
                if (user.getPasswordModifiedDate() != null && sdf.parse(passwordModifiedDate)
                        .compareTo(user.getPasswordModifiedDate()) <= 0) {
                    return ResponseEntity.ok(true);
                }
            } catch (ParseException e) {
                logger.error("Invalid date format.");
            }
        } else {
            throw new AuthenticationServiceException("no user");
        }
        return ResponseEntity.ok(false);
    }

    @Override
    public ResponseEntity<?> checkSSOToken(String token, String clientId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("X-Optima-Auth", token);
        HttpEntity httpEntity = new HttpEntity(httpHeaders);
        return restTemplate.exchange(this.ssoToken, HttpMethod.GET, httpEntity, Map.class, clientId);
    }

    private String getUserPasswordModifiedDate(Long clientId, String password) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        //headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        Map<String, Object> body = new HashMap<>();
        body.put("username", clientId);
        body.put("password", password);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        Map response = restTemplate.postForObject(loginUrl, request, Map.class);
        return objectMapper.convertValue(response.get("passwordModifiedDate"), String.class);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> getDataForUser(String clientId, String url) {
        HttpHeaders headers = new HttpHeaders();
        //headers.set("Authorization", "Bearer " + securityService.getToken().getAccessToken());
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(headers);
        return restTemplate.exchange(url, HttpMethod.GET, httpEntity, Map.class, clientId).getBody();
    }
}



