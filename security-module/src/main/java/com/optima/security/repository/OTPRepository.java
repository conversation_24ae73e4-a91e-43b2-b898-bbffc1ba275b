package com.optima.security.repository;

import com.optima.security.model.OTPRepositoryModel;
import com.optima.security.model.OTPStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.Optional;


public interface OTPRepository extends JpaRepository<OTPRepositoryModel, Long> {

    @Modifying
    @Query("delete from OTPRepositoryModel otpRepo where otpRepo.expirationTime < ?1 ")
    void deleteExpiredTokens(Date date);

    Optional<OTPRepositoryModel> findByUid(Long clientId);

    Optional<OTPRepositoryModel> findByUidAndStatus(Long clientId, OTPStatus status);

    @Modifying
    @Query("update OTPRepositoryModel otp set otp.status='EXPIRED' where otp.expirationTime < ?1 and otp.status='NEW'")
    void updateExpiredPasswords(Date date);

}


