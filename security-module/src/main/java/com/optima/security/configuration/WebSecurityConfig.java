package com.optima.security.configuration;


import com.optima.security.model.SecurityConfig;
import com.optima.security.service.impl.CustomUserDetailsService;
import com.optima.security.service.impl.TokenAuthenticationService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;


@Configuration
@EnableWebSecurity

public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final CustomUserDetailsService customUserDetailsService;

    private static final Logger logger = LogManager.getLogger(WebSecurityConfig.class);

    private final SecurityConfig securityConfig;

    @Autowired
    private LoggingFilter loggingFilter;

    private final Environment environment;
    /*@Value("${optima.security.admin.password}")*/
    private String adminPassword;
    /*@Value("${optima.security.admin.name}")*/
    private String adminName;

    public WebSecurityConfig(CustomUserDetailsService customUserDetailsService, Environment environment, SecurityConfig securityConfig) {
        this.customUserDetailsService = customUserDetailsService;
        this.environment = environment;
        this.securityConfig = securityConfig;
    }


    @Override
    protected void configure(HttpSecurity http) throws Exception {

        http.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and().authorizeRequests()
                .antMatchers(securityConfig.getPermittedResources().toArray(new String[0])).permitAll()
                .antMatchers(HttpMethod.POST, "/loginJWT").permitAll()
                .antMatchers(HttpMethod.POST, "/loginAdminJWT").permitAll()
                .antMatchers(HttpMethod.POST, "/api/forgotPassword").permitAll()
                .antMatchers(HttpMethod.POST, "/psw/forgotPassword").permitAll()
                .antMatchers(HttpMethod.POST, "/api/validSSOToken").permitAll()
                .anyRequest().authenticated()
                .and()
                //.formLogin()
                //.failureHandler(customAuthenticationFailureHandler)
                //.and()
                .addFilterBefore(loggingFilter, UsernamePasswordAuthenticationFilter.class)
                // We filter the api/login requests
                .addFilterBefore(this.jwtLoginFilter(),
                        UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(this.jwtAdminLoginFilter(),
                        UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(this.jwtSSOLoginFilter(),
                        UsernamePasswordAuthenticationFilter.class);

        // And filter other requests to check the presence of JWT in header
        http.addFilterBefore(this.jwtAuthenticationFilter(null),
                UsernamePasswordAuthenticationFilter.class);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationProvider(customUserDetailsService);
    }

    @Bean
    protected JWTLoginFilter jwtLoginFilter() throws Exception {
        JWTLoginFilter filter = new JWTLoginFilter("/loginJWT", authenticationManager());
        //filter.setAuthenticationFailureHandler(customAuthenticationFailureHandler);
        return filter;
    }

    @Bean
    protected JWTSSOLoginFilter jwtSSOLoginFilter() throws Exception {
        return new JWTSSOLoginFilter("/api/validSSOToken", authenticationManager());
    }

    @Bean
    protected JWTAdminLoginFilter jwtAdminLoginFilter() throws Exception {
        return new JWTAdminLoginFilter("/loginAdminJWT", authenticationManager(), environment);
    }

    @Bean
    protected JWTAuthenticationFilter jwtAuthenticationFilter(TokenAuthenticationService tokenAuthenticationService) {
        return new JWTAuthenticationFilter(tokenAuthenticationService);
    }

    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

}
