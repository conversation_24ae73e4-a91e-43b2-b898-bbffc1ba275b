package com.optima.common.models.wrappers.email;

import net.sf.oval.constraint.NotEmpty;
import net.sf.oval.constraint.NotNull;

public class EmailMessage {

    @NotNull
    @NotEmpty
    private String message;

    private String subject;

    private String utNumber;

    private String category;

    private String bills;

    private String subjectHeader;

    private String recipient;

    private String userType;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getUtNumber() {
        return utNumber;
    }

    public void setUtNumber(String utNumber) {
        this.utNumber = utNumber;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBills() {
        return bills;
    }

    public void setBills(String bills) {
        this.bills = bills;
    }

    public String getSubjectHeader() {
        return subjectHeader;
    }

    public void setSubjectHeader(String subjectHeader) {
        this.subjectHeader = subjectHeader;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }
}


