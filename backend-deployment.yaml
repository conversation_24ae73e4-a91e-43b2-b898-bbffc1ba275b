apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.21.0 (992df58d8)
  creationTimestamp: null
  labels:
    io.kompose.service: backend
  name: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: backend
  strategy: {}
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.21.0 (992df58d8)
      creationTimestamp: null
      labels:
        io.kompose.service: backend
    spec:
      containers:
      - env:
        - name: spring.profiles.active
          value: preprod
        image: salvatorematino/backend:latest
        imagePullPolicy: ""
        name: backend
        ports:
        - containerPort: 8080
        resources: {}
      restartPolicy: Always
      serviceAccountName: ""
      volumes: null
status: {}
